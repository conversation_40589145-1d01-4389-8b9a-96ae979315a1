{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/source/repos/ravihani/valtics/valtics-ai/components/ThemeToggle.tsx"], "sourcesContent": ["'use client';\n\nimport { useThemeSafe } from '@/contexts/ThemeContext';\nimport { useState, useEffect } from 'react';\n\nexport default function ThemeToggle() {\n  const [mounted, setMounted] = useState(false);\n  const [theme, setTheme] = useState<'light' | 'dark'>('light');\n\n  // Use safe version that returns null instead of throwing\n  const themeContext = useThemeSafe();\n\n  useEffect(() => {\n    setMounted(true);\n\n    // If no context, manage theme locally\n    if (!themeContext) {\n      const savedTheme = localStorage.getItem('theme') as 'light' | 'dark';\n      if (savedTheme) {\n        setTheme(savedTheme);\n        applyTheme(savedTheme);\n      } else {\n        const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\n        const initialTheme = systemPrefersDark ? 'dark' : 'light';\n        setTheme(initialTheme);\n        applyTheme(initialTheme);\n      }\n    }\n  }, [themeContext]);\n\n  const applyTheme = (newTheme: 'light' | 'dark') => {\n    const root = document.documentElement;\n    if (newTheme === 'dark') {\n      root.classList.add('dark');\n    } else {\n      root.classList.remove('dark');\n    }\n  };\n\n  const handleToggle = () => {\n    if (themeContext) {\n      // Use context if available\n      themeContext.toggleTheme();\n    } else {\n      // Handle locally if no context\n      const newTheme = theme === 'light' ? 'dark' : 'light';\n      setTheme(newTheme);\n      applyTheme(newTheme);\n      localStorage.setItem('theme', newTheme);\n    }\n  };\n\n  // Don't render until mounted to prevent hydration mismatch\n  if (!mounted) {\n    return (\n      <div className=\"p-2 w-9 h-9\">\n        {/* Placeholder to maintain layout */}\n      </div>\n    );\n  }\n\n  const currentTheme = themeContext ? themeContext.theme : theme;\n\n  return (\n    <button\n      onClick={handleToggle}\n      className=\"p-2 rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-100 dark:text-gray-300 dark:hover:text-gray-100 dark:hover:bg-gray-700 transition-colors\"\n      aria-label={`Switch to ${currentTheme === 'light' ? 'dark' : 'light'} mode`}\n      title={`Switch to ${currentTheme === 'light' ? 'dark' : 'light'} mode`}\n    >\n      {currentTheme === 'light' ? (\n        // Moon icon for dark mode\n        <svg\n          className=\"w-5 h-5\"\n          fill=\"none\"\n          stroke=\"currentColor\"\n          viewBox=\"0 0 24 24\"\n          xmlns=\"http://www.w3.org/2000/svg\"\n        >\n          <path\n            strokeLinecap=\"round\"\n            strokeLinejoin=\"round\"\n            strokeWidth={2}\n            d=\"M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z\"\n          />\n        </svg>\n      ) : (\n        // Sun icon for light mode\n        <svg\n          className=\"w-5 h-5\"\n          fill=\"none\"\n          stroke=\"currentColor\"\n          viewBox=\"0 0 24 24\"\n          xmlns=\"http://www.w3.org/2000/svg\"\n        >\n          <path\n            strokeLinecap=\"round\"\n            strokeLinejoin=\"round\"\n            strokeWidth={2}\n            d=\"M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z\"\n          />\n        </svg>\n      )}\n    </button>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKe,SAAS;;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;IAErD,yDAAyD;IACzD,MAAM,eAAe,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD;IAEhC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,WAAW;YAEX,sCAAsC;YACtC,IAAI,CAAC,cAAc;gBACjB,MAAM,aAAa,aAAa,OAAO,CAAC;gBACxC,IAAI,YAAY;oBACd,SAAS;oBACT,WAAW;gBACb,OAAO;oBACL,MAAM,oBAAoB,OAAO,UAAU,CAAC,gCAAgC,OAAO;oBACnF,MAAM,eAAe,oBAAoB,SAAS;oBAClD,SAAS;oBACT,WAAW;gBACb;YACF;QACF;gCAAG;QAAC;KAAa;IAEjB,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,SAAS,eAAe;QACrC,IAAI,aAAa,QAAQ;YACvB,KAAK,SAAS,CAAC,GAAG,CAAC;QACrB,OAAO;YACL,KAAK,SAAS,CAAC,MAAM,CAAC;QACxB;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,cAAc;YAChB,2BAA2B;YAC3B,aAAa,WAAW;QAC1B,OAAO;YACL,+BAA+B;YAC/B,MAAM,WAAW,UAAU,UAAU,SAAS;YAC9C,SAAS;YACT,WAAW;YACX,aAAa,OAAO,CAAC,SAAS;QAChC;IACF;IAEA,2DAA2D;IAC3D,IAAI,CAAC,SAAS;QACZ,qBACE,6LAAC;YAAI,WAAU;;;;;;IAInB;IAEA,MAAM,eAAe,eAAe,aAAa,KAAK,GAAG;IAEzD,qBACE,6LAAC;QACC,SAAS;QACT,WAAU;QACV,cAAY,CAAC,UAAU,EAAE,iBAAiB,UAAU,SAAS,QAAQ,KAAK,CAAC;QAC3E,OAAO,CAAC,UAAU,EAAE,iBAAiB,UAAU,SAAS,QAAQ,KAAK,CAAC;kBAErE,iBAAiB,UAChB,0BAA0B;sBAC1B,6LAAC;YACC,WAAU;YACV,MAAK;YACL,QAAO;YACP,SAAQ;YACR,OAAM;sBAEN,cAAA,6LAAC;gBACC,eAAc;gBACd,gBAAe;gBACf,aAAa;gBACb,GAAE;;;;;;;;;;mBAIN,0BAA0B;sBAC1B,6LAAC;YACC,WAAU;YACV,MAAK;YACL,QAAO;YACP,SAAQ;YACR,OAAM;sBAEN,cAAA,6LAAC;gBACC,eAAc;gBACd,gBAAe;gBACf,aAAa;gBACb,GAAE;;;;;;;;;;;;;;;;AAMd;GApGwB;;QAKD,4HAAA,CAAA,eAAY;;;KALX", "debugId": null}}, {"offset": {"line": 146, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/source/repos/ravihani/valtics/valtics-ai/components/TrialBanner.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { getTrialStatusMessage } from '@/lib/trial';\n\nexport default function TrialBanner() {\n  const { user } = useAuth();\n  const [isDismissed, setIsDismissed] = useState(false);\n\n  // Don't show banner if user is not a trial user, is admin, or banner is dismissed\n  if (!user || !user.isTrialUser || user.role === 'admin' || isDismissed) {\n    return null;\n  }\n\n  const { message, type, daysRemaining } = getTrialStatusMessage(user);\n\n  // Don't show banner if no message\n  if (!message) {\n    return null;\n  }\n\n  const getBannerStyles = () => {\n    switch (type) {\n      case 'error':\n        return 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800 text-red-800 dark:text-red-200';\n      case 'warning':\n        return 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800 text-yellow-800 dark:text-yellow-200';\n      default:\n        return 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800 text-blue-800 dark:text-blue-200';\n    }\n  };\n\n  const getButtonStyles = () => {\n    switch (type) {\n      case 'error':\n        return 'bg-red-600 dark:bg-red-700 hover:bg-red-700 dark:hover:bg-red-800 text-white';\n      case 'warning':\n        return 'bg-yellow-600 dark:bg-yellow-700 hover:bg-yellow-700 dark:hover:bg-yellow-800 text-white';\n      default:\n        return 'bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-800 text-white';\n    }\n  };\n\n  const getIconStyles = () => {\n    switch (type) {\n      case 'error':\n        return 'text-red-400 dark:text-red-300';\n      case 'warning':\n        return 'text-yellow-400 dark:text-yellow-300';\n      default:\n        return 'text-blue-400 dark:text-blue-300';\n    }\n  };\n\n  return (\n    <div className={`border-l-4 p-4 ${getBannerStyles()}`}>\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex items-center\">\n          <div className=\"flex-shrink-0\">\n            {type === 'error' ? (\n              <svg className={`h-5 w-5 ${getIconStyles()}`} viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\n              </svg>\n            ) : type === 'warning' ? (\n              <svg className={`h-5 w-5 ${getIconStyles()}`} viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                <path fillRule=\"evenodd\" d=\"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\n              </svg>\n            ) : (\n              <svg className={`h-5 w-5 ${getIconStyles()}`} viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\" clipRule=\"evenodd\" />\n              </svg>\n            )}\n          </div>\n          <div className=\"ml-3\">\n            <p className=\"text-sm font-medium\">\n              {message}\n            </p>\n          </div>\n        </div>\n        \n        <div className=\"flex items-center space-x-3\">\n          <Link\n            href=\"/pricing\"\n            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${getButtonStyles()}`}\n          >\n            Upgrade Now\n          </Link>\n          \n          <button\n            onClick={() => setIsDismissed(true)}\n            className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\n            aria-label=\"Dismiss banner\"\n          >\n            <svg className=\"h-5 w-5\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n              <path fillRule=\"evenodd\" d=\"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\" clipRule=\"evenodd\" />\n            </svg>\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n}\n\n// Compact version for navigation bar\nexport function TrialIndicator() {\n  const { user } = useAuth();\n\n  if (!user || !user.isTrialUser || user.role === 'admin') {\n    return null;\n  }\n\n  const { daysRemaining } = getTrialStatusMessage(user);\n\n  if (daysRemaining <= 0) {\n    return (\n      <Link\n        href=\"/pricing\"\n        className=\"px-3 py-1 bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 text-xs font-medium rounded-full hover:bg-red-200 dark:hover:bg-red-800 transition-colors\"\n      >\n        Trial Expired\n      </Link>\n    );\n  }\n\n  const getIndicatorStyles = () => {\n    if (daysRemaining <= 3) {\n      return 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 hover:bg-yellow-200 dark:hover:bg-yellow-800';\n    }\n    return 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 hover:bg-blue-200 dark:hover:bg-blue-800';\n  };\n\n  return (\n    <Link\n      href=\"/pricing\"\n      className={`px-3 py-1 text-xs font-medium rounded-full transition-colors ${getIndicatorStyles()}`}\n    >\n      {daysRemaining} day{daysRemaining === 1 ? '' : 's'} left\n    </Link>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAOe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,kFAAkF;IAClF,IAAI,CAAC,QAAQ,CAAC,KAAK,WAAW,IAAI,KAAK,IAAI,KAAK,WAAW,aAAa;QACtE,OAAO;IACT;IAEA,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,+GAAA,CAAA,wBAAqB,AAAD,EAAE;IAE/D,kCAAkC;IAClC,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,MAAM,kBAAkB;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,kBAAkB;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,eAAe,EAAE,mBAAmB;kBACnD,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACZ,SAAS,wBACR,6LAAC;gCAAI,WAAW,CAAC,QAAQ,EAAE,iBAAiB;gCAAE,SAAQ;gCAAY,MAAK;0CACrE,cAAA,6LAAC;oCAAK,UAAS;oCAAU,GAAE;oCAA0N,UAAS;;;;;;;;;;uCAE9P,SAAS,0BACX,6LAAC;gCAAI,WAAW,CAAC,QAAQ,EAAE,iBAAiB;gCAAE,SAAQ;gCAAY,MAAK;0CACrE,cAAA,6LAAC;oCAAK,UAAS;oCAAU,GAAE;oCAAoN,UAAS;;;;;;;;;;qDAG1P,6LAAC;gCAAI,WAAW,CAAC,QAAQ,EAAE,iBAAiB;gCAAE,SAAQ;gCAAY,MAAK;0CACrE,cAAA,6LAAC;oCAAK,UAAS;oCAAU,GAAE;oCAAmI,UAAS;;;;;;;;;;;;;;;;sCAI7K,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;0CACV;;;;;;;;;;;;;;;;;8BAKP,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,+JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAW,CAAC,2DAA2D,EAAE,mBAAmB;sCAC7F;;;;;;sCAID,6LAAC;4BACC,SAAS,IAAM,eAAe;4BAC9B,WAAU;4BACV,cAAW;sCAEX,cAAA,6LAAC;gCAAI,WAAU;gCAAU,SAAQ;gCAAY,MAAK;0CAChD,cAAA,6LAAC;oCAAK,UAAS;oCAAU,GAAE;oCAAqM,UAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOvP;GAhGwB;;QACL,2HAAA,CAAA,UAAO;;;KADF;AAmGjB,SAAS;;IACd,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,UAAO,AAAD;IAEvB,IAAI,CAAC,QAAQ,CAAC,KAAK,WAAW,IAAI,KAAK,IAAI,KAAK,SAAS;QACvD,OAAO;IACT;IAEA,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,+GAAA,CAAA,wBAAqB,AAAD,EAAE;IAEhD,IAAI,iBAAiB,GAAG;QACtB,qBACE,6LAAC,+JAAA,CAAA,UAAI;YACH,MAAK;YACL,WAAU;sBACX;;;;;;IAIL;IAEA,MAAM,qBAAqB;QACzB,IAAI,iBAAiB,GAAG;YACtB,OAAO;QACT;QACA,OAAO;IACT;IAEA,qBACE,6LAAC,+JAAA,CAAA,UAAI;QACH,MAAK;QACL,WAAW,CAAC,6DAA6D,EAAE,sBAAsB;;YAEhG;YAAc;YAAK,kBAAkB,IAAI,KAAK;YAAI;;;;;;;AAGzD;IAnCgB;;QACG,2HAAA,CAAA,UAAO;;;MADV", "debugId": null}}, {"offset": {"line": 413, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/source/repos/ravihani/valtics/valtics-ai/components/Navigation.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { useRouter } from 'next/navigation';\nimport ThemeToggle from './ThemeToggle';\nimport { TrialIndicator } from './TrialBanner';\n\ninterface NavigationProps {\n  title?: string;\n  showBackButton?: boolean;\n  backUrl?: string;\n  backText?: string;\n}\n\nexport default function Navigation({\n  title = 'VALTICS AI',\n  showBackButton = false,\n  backUrl = '/dashboard',\n  backText = '← Back to Dashboard'\n}: NavigationProps) {\n  const { user, logOut, isAdmin } = useAuth();\n  const router = useRouter();\n\n  const handleLogout = async () => {\n    try {\n      await logOut();\n      router.push('/');\n    } catch (error) {\n      console.error('Error logging out:', error);\n    }\n  };\n\n  if (!user) {\n    return null;\n  }\n\n  return (\n    <nav className=\"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between h-16\">\n          <div className=\"flex items-center\">\n            <Link href=\"/dashboard\" className=\"flex items-center space-x-3\">\n              <Image\n                src=\"/logo.png\"\n                alt=\"VALTICS AI Logo\"\n                width={32}\n                height={32}\n                className=\"w-8 h-8\"\n              />\n              <span className=\"text-xl font-semibold text-gray-900 dark:text-white\">\n                {title}\n              </span>\n            </Link>\n          </div>\n\n          <div className=\"flex items-center space-x-4\">\n            {showBackButton && (\n              <Link\n                href={backUrl}\n                className=\"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium\"\n              >\n                {backText}\n              </Link>\n            )}\n\n            {!showBackButton && (\n              <>\n                <Link\n                  href=\"/brands\"\n                  className=\"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium\"\n                >\n                  Brands\n                </Link>\n                <Link\n                  href=\"/templates\"\n                  className=\"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium\"\n                >\n                  Templates\n                </Link>\n                {isAdmin && (\n                  <Link\n                    href=\"/admin\"\n                    className=\"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium\"\n                  >\n                    Admin\n                  </Link>\n                )}\n                <Link\n                  href=\"/profile\"\n                  className=\"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium\"\n                >\n                  Profile\n                </Link>\n              </>\n            )}\n\n            {/* Trial Indicator */}\n            <TrialIndicator />\n\n            {/* Theme Toggle */}\n            <ThemeToggle />\n\n            <button\n              onClick={handleLogout}\n              className=\"bg-red-600 dark:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-red-700 dark:hover:bg-red-800\"\n            >\n              Logout\n            </button>\n          </div>\n        </div>\n      </div>\n    </nav>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AAgBe,SAAS,WAAW,EACjC,QAAQ,YAAY,EACpB,iBAAiB,KAAK,EACtB,UAAU,YAAY,EACtB,WAAW,qBAAqB,EAChB;;IAChB,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,UAAO,AAAD;IACxC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe;QACnB,IAAI;YACF,MAAM;YACN,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;QACtC;IACF;IAEA,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAa,WAAU;;8CAChC,6LAAC,gIAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,OAAO;oCACP,QAAQ;oCACR,WAAU;;;;;;8CAEZ,6LAAC;oCAAK,WAAU;8CACb;;;;;;;;;;;;;;;;;kCAKP,6LAAC;wBAAI,WAAU;;4BACZ,gCACC,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAM;gCACN,WAAU;0CAET;;;;;;4BAIJ,CAAC,gCACA;;kDACE,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;oCAGA,yBACC,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAIH,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;0CAOL,6LAAC,6HAAA,CAAA,iBAAc;;;;;0CAGf,6LAAC,6HAAA,CAAA,UAAW;;;;;0CAEZ,6LAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;GAnGwB;;QAMY,2HAAA,CAAA,UAAO;QAC1B,qIAAA,CAAA,YAAS;;;KAPF", "debugId": null}}, {"offset": {"line": 602, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/source/repos/ravihani/valtics/valtics-ai/app/profile/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { useRouter } from 'next/navigation';\nimport Link from 'next/link';\nimport { doc, getDoc, updateDoc, collection, query, where, getDocs, orderBy } from 'firebase/firestore';\nimport { db } from '@/lib/firebase/config';\nimport { User, BVAInstance, Subscription } from '@/types';\nimport Navigation from '@/components/Navigation';\n\nexport default function Profile() {\n  const { user, firebaseUser, loading, logOut } = useAuth();\n  const router = useRouter();\n\n  const [userProfile, setUserProfile] = useState<User | null>(null);\n  const [subscription, setSubscription] = useState<Subscription | null>(null);\n  const [bvaInstances, setBvaInstances] = useState<BVAInstance[]>([]);\n  const [loadingData, setLoadingData] = useState(true);\n  const [activeTab, setActiveTab] = useState('profile');\n  const [updating, setUpdating] = useState(false);\n\n  useEffect(() => {\n    if (!loading && !user) {\n      router.push('/login');\n    }\n  }, [user, loading, router]);\n\n  useEffect(() => {\n    if (user && firebaseUser) {\n      fetchProfileData();\n    }\n  }, [user, firebaseUser]);\n\n  const fetchProfileData = async () => {\n    if (!firebaseUser) return;\n\n    try {\n      setLoadingData(true);\n\n      // Fetch user profile\n      const userDoc = await getDoc(doc(db, 'users', firebaseUser.uid));\n      if (userDoc.exists()) {\n        setUserProfile({ id: userDoc.id, ...userDoc.data() } as User);\n      }\n\n      // Fetch subscription\n      const subscriptionQuery = query(\n        collection(db, 'subscriptions'),\n        where('userId', '==', firebaseUser.uid)\n      );\n      const subscriptionSnapshot = await getDocs(subscriptionQuery);\n      if (!subscriptionSnapshot.empty) {\n        const subscriptionData = subscriptionSnapshot.docs[0];\n        setSubscription({ id: subscriptionData.id, ...subscriptionData.data() } as Subscription);\n      }\n\n      // Fetch user's BVA instances\n      const bvaQuery = query(\n        collection(db, 'bvaInstances'),\n        where('userId', '==', firebaseUser.uid),\n        orderBy('updatedAt', 'desc')\n      );\n      const bvaSnapshot = await getDocs(bvaQuery);\n      const bvaData = bvaSnapshot.docs.map(doc => ({\n        id: doc.id,\n        ...doc.data()\n      })) as BVAInstance[];\n      setBvaInstances(bvaData);\n\n    } catch (error) {\n      console.error('Error fetching profile data:', error);\n    } finally {\n      setLoadingData(false);\n    }\n  };\n\n  const handleLogout = async () => {\n    try {\n      await logOut();\n      router.push('/');\n    } catch (error) {\n      console.error('Error logging out:', error);\n    }\n  };\n\n  const updateProfile = async (updates: Partial<User>) => {\n    if (!firebaseUser || !userProfile) return;\n\n    try {\n      setUpdating(true);\n      await updateDoc(doc(db, 'users', firebaseUser.uid), updates);\n      setUserProfile({ ...userProfile, ...updates });\n    } catch (error) {\n      console.error('Error updating profile:', error);\n      alert('Error updating profile. Please try again.');\n    } finally {\n      setUpdating(false);\n    }\n  };\n\n  if (loading || loadingData) {\n    return (\n      <div className=\"flex min-h-screen items-center justify-center bg-gray-50 dark:bg-gray-900\">\n        <div className=\"text-xl text-gray-900 dark:text-white\">Loading...</div>\n      </div>\n    );\n  }\n\n  if (!user || !userProfile) {\n    return null;\n  }\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'completed':\n        return 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200';\n      case 'in-progress':\n        return 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200';\n      case 'draft':\n        return 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200';\n      default:\n        return 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200';\n    }\n  };\n\n  const renderProfile = () => (\n    <div className=\"space-y-6\">\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow\">\n        <div className=\"p-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">Account Information</h3>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                Email Address\n              </label>\n              <input\n                type=\"email\"\n                value={firebaseUser?.email || ''}\n                disabled\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-gray-500 dark:text-gray-400\"\n              />\n              <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">Email cannot be changed</p>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                Account Type\n              </label>\n              <input\n                type=\"text\"\n                value={userProfile.role === 'admin' ? 'Administrator' : 'Standard User'}\n                disabled\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-gray-500 dark:text-gray-400\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                Member Since\n              </label>\n              <input\n                type=\"text\"\n                value={new Date(userProfile.createdAt).toLocaleDateString()}\n                disabled\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-gray-500 dark:text-gray-400\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                Total BVAs Created\n              </label>\n              <input\n                type=\"text\"\n                value={bvaInstances.length.toString()}\n                disabled\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-gray-500 dark:text-gray-400\"\n              />\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow\">\n        <div className=\"p-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">Account Actions</h3>\n\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center justify-between p-4 border border-gray-200 dark:border-gray-600 rounded-lg\">\n              <div>\n                <h4 className=\"font-medium text-gray-900 dark:text-white\">Change Password</h4>\n                <p className=\"text-sm text-gray-500 dark:text-gray-400\">Update your account password</p>\n              </div>\n              <button className=\"bg-blue-600 dark:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 dark:hover:bg-blue-800\">\n                Change Password\n              </button>\n            </div>\n\n            <div className=\"flex items-center justify-between p-4 border border-gray-200 dark:border-gray-600 rounded-lg\">\n              <div>\n                <h4 className=\"font-medium text-gray-900 dark:text-white\">Download Data</h4>\n                <p className=\"text-sm text-gray-500 dark:text-gray-400\">Export all your BVA data</p>\n              </div>\n              <button className=\"bg-green-600 dark:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-green-700 dark:hover:bg-green-800\">\n                Export Data\n              </button>\n            </div>\n\n            <div className=\"flex items-center justify-between p-4 border border-red-200 dark:border-red-600 rounded-lg\">\n              <div>\n                <h4 className=\"font-medium text-red-900 dark:text-red-400\">Sign Out</h4>\n                <p className=\"text-sm text-red-600 dark:text-red-400\">Sign out of your account</p>\n              </div>\n              <button\n                onClick={handleLogout}\n                className=\"bg-red-600 dark:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-red-700 dark:hover:bg-red-800\"\n              >\n                Sign Out\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n\n  const renderSubscription = () => (\n    <div className=\"space-y-6\">\n      {subscription ? (\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow\">\n          <div className=\"p-6\">\n            <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">Current Subscription</h3>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Plan Type\n                </label>\n                <div className=\"flex items-center\">\n                  <span className={`px-3 py-1 rounded-full text-sm font-medium ${\n                    subscription.type === 'enterprise' ? 'bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200' :\n                    subscription.type === 'premium' ? 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200' :\n                    'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200'\n                  }`}>\n                    {subscription.type.charAt(0).toUpperCase() + subscription.type.slice(1)}\n                  </span>\n                </div>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Status\n                </label>\n                <span className={`px-3 py-1 rounded-full text-sm font-medium ${\n                  subscription.status === 'active' ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' :\n                  subscription.status === 'cancelled' ? 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200' :\n                  'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200'\n                }`}>\n                  {subscription.status.charAt(0).toUpperCase() + subscription.status.slice(1)}\n                </span>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Start Date\n                </label>\n                <p className=\"text-sm text-gray-900 dark:text-white\">\n                  {new Date(subscription.startDate).toLocaleDateString()}\n                </p>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  End Date\n                </label>\n                <p className=\"text-sm text-gray-900 dark:text-white\">\n                  {new Date(subscription.endDate).toLocaleDateString()}\n                </p>\n              </div>\n\n              <div className=\"md:col-span-2\">\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Template Access\n                </label>\n                <p className=\"text-sm text-gray-900 dark:text-white\">\n                  {subscription.accessibleTemplateIds.length} templates available\n                </p>\n              </div>\n            </div>\n\n            <div className=\"mt-6 flex space-x-4\">\n              <button className=\"bg-blue-600 dark:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 dark:hover:bg-blue-800\">\n                Upgrade Plan\n              </button>\n              <button className=\"bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-400 dark:hover:bg-gray-500\">\n                Manage Billing\n              </button>\n            </div>\n          </div>\n        </div>\n      ) : (\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow\">\n          <div className=\"p-6 text-center\">\n            <div className=\"text-gray-400 dark:text-gray-500 text-6xl mb-4\">💳</div>\n            <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">No Active Subscription</h3>\n            <p className=\"text-gray-600 dark:text-gray-300 mb-6\">\n              Subscribe to access premium templates and features.\n            </p>\n            <button className=\"bg-blue-600 dark:bg-blue-700 text-white px-6 py-3 rounded-md font-medium hover:bg-blue-700 dark:hover:bg-blue-800\">\n              View Subscription Plans\n            </button>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n\n  const renderBVAs = () => (\n    <div className=\"space-y-6\">\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow\">\n        <div className=\"p-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">Your BVAs</h3>\n\n          {bvaInstances.length > 0 ? (\n            <div className=\"space-y-4\">\n              {bvaInstances.map((bva) => (\n                <div key={bva.id} className=\"border border-gray-200 dark:border-gray-600 rounded-lg p-4\">\n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <h4 className=\"font-medium text-gray-900 dark:text-white\">{bva.name}</h4>\n                      <p className=\"text-sm text-gray-500 dark:text-gray-400\">Client: {bva.clientName}</p>\n                      <p className=\"text-xs text-gray-400 dark:text-gray-500\">\n                        Last updated: {new Date(bva.updatedAt).toLocaleDateString()}\n                      </p>\n                    </div>\n                    <div className=\"flex items-center space-x-3\">\n                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(bva.status)}`}>\n                        {bva.status}\n                      </span>\n                      <Link\n                        href={`/bva/${bva.id}`}\n                        className=\"text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm font-medium\"\n                      >\n                        View\n                      </Link>\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          ) : (\n            <div className=\"text-center py-8\">\n              <div className=\"text-gray-400 dark:text-gray-500 text-4xl mb-4\">📊</div>\n              <p className=\"text-gray-500 dark:text-gray-400\">No BVAs created yet.</p>\n              <Link\n                href=\"/bva/new\"\n                className=\"mt-4 inline-block bg-blue-600 dark:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 dark:hover:bg-blue-800\"\n              >\n                Create Your First BVA\n              </Link>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900\">\n      <Navigation title=\"VALTICS AI\" showBackButton={true} />\n\n      {/* Main Content */}\n      <div className=\"max-w-4xl mx-auto py-6 sm:px-6 lg:px-8\">\n        <div className=\"px-4 py-6 sm:px-0\">\n          {/* Header */}\n          <div className=\"mb-8\">\n            <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white\">Profile & Settings</h1>\n            <p className=\"mt-2 text-gray-600 dark:text-gray-300\">\n              Manage your account settings and view your activity.\n            </p>\n          </div>\n\n          {/* Tabs */}\n          <div className=\"mb-8\">\n            <nav className=\"flex space-x-8\">\n              {[\n                { id: 'profile', name: 'Profile' },\n                { id: 'subscription', name: 'Subscription' },\n                { id: 'bvas', name: 'My BVAs' }\n              ].map((tab) => (\n                <button\n                  key={tab.id}\n                  onClick={() => setActiveTab(tab.id)}\n                  className={`py-2 px-1 border-b-2 font-medium text-sm ${\n                    activeTab === tab.id\n                      ? 'border-blue-500 text-blue-600 dark:text-blue-400'\n                      : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'\n                  }`}\n                >\n                  {tab.name}\n                </button>\n              ))}\n            </nav>\n          </div>\n\n          {/* Tab Content */}\n          {activeTab === 'profile' && renderProfile()}\n          {activeTab === 'subscription' && renderSubscription()}\n          {activeTab === 'bvas' && renderBVAs()}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AACA;AAEA;;;AATA;;;;;;;;AAWe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,YAAY,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,UAAO,AAAD;IACtD,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC5D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB;IACtE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAClE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,IAAI,CAAC,WAAW,CAAC,MAAM;gBACrB,OAAO,IAAI,CAAC;YACd;QACF;4BAAG;QAAC;QAAM;QAAS;KAAO;IAE1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,IAAI,QAAQ,cAAc;gBACxB;YACF;QACF;4BAAG;QAAC;QAAM;KAAa;IAEvB,MAAM,mBAAmB;QACvB,IAAI,CAAC,cAAc;QAEnB,IAAI;YACF,eAAe;YAEf,qBAAqB;YACrB,MAAM,UAAU,MAAM,CAAA,GAAA,sKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,4HAAA,CAAA,KAAE,EAAE,SAAS,aAAa,GAAG;YAC9D,IAAI,QAAQ,MAAM,IAAI;gBACpB,eAAe;oBAAE,IAAI,QAAQ,EAAE;oBAAE,GAAG,QAAQ,IAAI,EAAE;gBAAC;YACrD;YAEA,qBAAqB;YACrB,MAAM,oBAAoB,CAAA,GAAA,sKAAA,CAAA,QAAK,AAAD,EAC5B,CAAA,GAAA,sKAAA,CAAA,aAAU,AAAD,EAAE,4HAAA,CAAA,KAAE,EAAE,kBACf,CAAA,GAAA,sKAAA,CAAA,QAAK,AAAD,EAAE,UAAU,MAAM,aAAa,GAAG;YAExC,MAAM,uBAAuB,MAAM,CAAA,GAAA,sKAAA,CAAA,UAAO,AAAD,EAAE;YAC3C,IAAI,CAAC,qBAAqB,KAAK,EAAE;gBAC/B,MAAM,mBAAmB,qBAAqB,IAAI,CAAC,EAAE;gBACrD,gBAAgB;oBAAE,IAAI,iBAAiB,EAAE;oBAAE,GAAG,iBAAiB,IAAI,EAAE;gBAAC;YACxE;YAEA,6BAA6B;YAC7B,MAAM,WAAW,CAAA,GAAA,sKAAA,CAAA,QAAK,AAAD,EACnB,CAAA,GAAA,sKAAA,CAAA,aAAU,AAAD,EAAE,4HAAA,CAAA,KAAE,EAAE,iBACf,CAAA,GAAA,sKAAA,CAAA,QAAK,AAAD,EAAE,UAAU,MAAM,aAAa,GAAG,GACtC,CAAA,GAAA,sKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;YAEvB,MAAM,cAAc,MAAM,CAAA,GAAA,sKAAA,CAAA,UAAO,AAAD,EAAE;YAClC,MAAM,UAAU,YAAY,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;oBAC3C,IAAI,IAAI,EAAE;oBACV,GAAG,IAAI,IAAI,EAAE;gBACf,CAAC;YACD,gBAAgB;QAElB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,eAAe;QACnB,IAAI;YACF,MAAM;YACN,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;QACtC;IACF;IAEA,MAAM,gBAAgB,OAAO;QAC3B,IAAI,CAAC,gBAAgB,CAAC,aAAa;QAEnC,IAAI;YACF,YAAY;YACZ,MAAM,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,4HAAA,CAAA,KAAE,EAAE,SAAS,aAAa,GAAG,GAAG;YACpD,eAAe;gBAAE,GAAG,WAAW;gBAAE,GAAG,OAAO;YAAC;QAC9C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM;QACR,SAAU;YACR,YAAY;QACd;IACF;IAEA,IAAI,WAAW,aAAa;QAC1B,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BAAwC;;;;;;;;;;;IAG7D;IAEA,IAAI,CAAC,QAAQ,CAAC,aAAa;QACzB,OAAO;IACT;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB,kBACpB,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAyD;;;;;;0CAEvE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAAkE;;;;;;0DAGnF,6LAAC;gDACC,MAAK;gDACL,OAAO,cAAc,SAAS;gDAC9B,QAAQ;gDACR,WAAU;;;;;;0DAEZ,6LAAC;gDAAE,WAAU;0DAAgD;;;;;;;;;;;;kDAG/D,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAAkE;;;;;;0DAGnF,6LAAC;gDACC,MAAK;gDACL,OAAO,YAAY,IAAI,KAAK,UAAU,kBAAkB;gDACxD,QAAQ;gDACR,WAAU;;;;;;;;;;;;kDAId,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAAkE;;;;;;0DAGnF,6LAAC;gDACC,MAAK;gDACL,OAAO,IAAI,KAAK,YAAY,SAAS,EAAE,kBAAkB;gDACzD,QAAQ;gDACR,WAAU;;;;;;;;;;;;kDAId,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAAkE;;;;;;0DAGnF,6LAAC;gDACC,MAAK;gDACL,OAAO,aAAa,MAAM,CAAC,QAAQ;gDACnC,QAAQ;gDACR,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAOpB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAyD;;;;;;0CAEvE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAA4C;;;;;;kEAC1D,6LAAC;wDAAE,WAAU;kEAA2C;;;;;;;;;;;;0DAE1D,6LAAC;gDAAO,WAAU;0DAA4H;;;;;;;;;;;;kDAKhJ,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAA4C;;;;;;kEAC1D,6LAAC;wDAAE,WAAU;kEAA2C;;;;;;;;;;;;0DAE1D,6LAAC;gDAAO,WAAU;0DAAgI;;;;;;;;;;;;kDAKpJ,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,6LAAC;wDAAE,WAAU;kEAAyC;;;;;;;;;;;;0DAExD,6LAAC;gDACC,SAAS;gDACT,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAUb,MAAM,qBAAqB,kBACzB,6LAAC;YAAI,WAAU;sBACZ,6BACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAyD;;;;;;sCAEvE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAW,CAAC,2CAA2C,EAC3D,aAAa,IAAI,KAAK,eAAe,0EACrC,aAAa,IAAI,KAAK,YAAY,kEAClC,iEACA;0DACC,aAAa,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,aAAa,IAAI,CAAC,KAAK,CAAC;;;;;;;;;;;;;;;;;8CAK3E,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,6LAAC;4CAAK,WAAW,CAAC,2CAA2C,EAC3D,aAAa,MAAM,KAAK,WAAW,sEACnC,aAAa,MAAM,KAAK,cAAc,8DACtC,iEACA;sDACC,aAAa,MAAM,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,aAAa,MAAM,CAAC,KAAK,CAAC;;;;;;;;;;;;8CAI7E,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,6LAAC;4CAAE,WAAU;sDACV,IAAI,KAAK,aAAa,SAAS,EAAE,kBAAkB;;;;;;;;;;;;8CAIxD,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,6LAAC;4CAAE,WAAU;sDACV,IAAI,KAAK,aAAa,OAAO,EAAE,kBAAkB;;;;;;;;;;;;8CAItD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,6LAAC;4CAAE,WAAU;;gDACV,aAAa,qBAAqB,CAAC,MAAM;gDAAC;;;;;;;;;;;;;;;;;;;sCAKjD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAO,WAAU;8CAA4H;;;;;;8CAG9I,6LAAC;oCAAO,WAAU;8CAAkJ;;;;;;;;;;;;;;;;;;;;;;qCAO1K,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCAAiD;;;;;;sCAChE,6LAAC;4BAAG,WAAU;sCAAyD;;;;;;sCACvE,6LAAC;4BAAE,WAAU;sCAAwC;;;;;;sCAGrD,6LAAC;4BAAO,WAAU;sCAAoH;;;;;;;;;;;;;;;;;;;;;;IAShJ,MAAM,aAAa,kBACjB,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAyD;;;;;;wBAEtE,aAAa,MAAM,GAAG,kBACrB,6LAAC;4BAAI,WAAU;sCACZ,aAAa,GAAG,CAAC,CAAC,oBACjB,6LAAC;oCAAiB,WAAU;8CAC1B,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAA6C,IAAI,IAAI;;;;;;kEACnE,6LAAC;wDAAE,WAAU;;4DAA2C;4DAAS,IAAI,UAAU;;;;;;;kEAC/E,6LAAC;wDAAE,WAAU;;4DAA2C;4DACvC,IAAI,KAAK,IAAI,SAAS,EAAE,kBAAkB;;;;;;;;;;;;;0DAG7D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAW,CAAC,2CAA2C,EAAE,eAAe,IAAI,MAAM,GAAG;kEACxF,IAAI,MAAM;;;;;;kEAEb,6LAAC,+JAAA,CAAA,UAAI;wDACH,MAAM,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE;wDACtB,WAAU;kEACX;;;;;;;;;;;;;;;;;;mCAhBG,IAAI,EAAE;;;;;;;;;iDAyBpB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAAiD;;;;;;8CAChE,6LAAC;oCAAE,WAAU;8CAAmC;;;;;;8CAChD,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAUb,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,4HAAA,CAAA,UAAU;gBAAC,OAAM;gBAAa,gBAAgB;;;;;;0BAG/C,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAmD;;;;;;8CACjE,6LAAC;oCAAE,WAAU;8CAAwC;;;;;;;;;;;;sCAMvD,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACZ;oCACC;wCAAE,IAAI;wCAAW,MAAM;oCAAU;oCACjC;wCAAE,IAAI;wCAAgB,MAAM;oCAAe;oCAC3C;wCAAE,IAAI;wCAAQ,MAAM;oCAAU;iCAC/B,CAAC,GAAG,CAAC,CAAC,oBACL,6LAAC;wCAEC,SAAS,IAAM,aAAa,IAAI,EAAE;wCAClC,WAAW,CAAC,yCAAyC,EACnD,cAAc,IAAI,EAAE,GAChB,qDACA,qJACJ;kDAED,IAAI,IAAI;uCARJ,IAAI,EAAE;;;;;;;;;;;;;;;wBAelB,cAAc,aAAa;wBAC3B,cAAc,kBAAkB;wBAChC,cAAc,UAAU;;;;;;;;;;;;;;;;;;AAKnC;GApZwB;;QAC0B,2HAAA,CAAA,UAAO;QACxC,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}]}