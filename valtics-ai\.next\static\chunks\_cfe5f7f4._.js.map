{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/source/repos/ravihani/valtics/valtics-ai/components/ThemeToggle.tsx"], "sourcesContent": ["'use client';\n\nimport { useThemeSafe } from '@/contexts/ThemeContext';\nimport { useState, useEffect } from 'react';\n\nexport default function ThemeToggle() {\n  const [mounted, setMounted] = useState(false);\n  const [theme, setTheme] = useState<'light' | 'dark'>('light');\n\n  // Use safe version that returns null instead of throwing\n  const themeContext = useThemeSafe();\n\n  useEffect(() => {\n    setMounted(true);\n\n    // If no context, manage theme locally\n    if (!themeContext) {\n      const savedTheme = localStorage.getItem('theme') as 'light' | 'dark';\n      if (savedTheme) {\n        setTheme(savedTheme);\n        applyTheme(savedTheme);\n      } else {\n        const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\n        const initialTheme = systemPrefersDark ? 'dark' : 'light';\n        setTheme(initialTheme);\n        applyTheme(initialTheme);\n      }\n    }\n  }, [themeContext]);\n\n  const applyTheme = (newTheme: 'light' | 'dark') => {\n    const root = document.documentElement;\n    if (newTheme === 'dark') {\n      root.classList.add('dark');\n    } else {\n      root.classList.remove('dark');\n    }\n  };\n\n  const handleToggle = () => {\n    if (themeContext) {\n      // Use context if available\n      themeContext.toggleTheme();\n    } else {\n      // Handle locally if no context\n      const newTheme = theme === 'light' ? 'dark' : 'light';\n      setTheme(newTheme);\n      applyTheme(newTheme);\n      localStorage.setItem('theme', newTheme);\n    }\n  };\n\n  // Don't render until mounted to prevent hydration mismatch\n  if (!mounted) {\n    return (\n      <div className=\"p-2 w-9 h-9\">\n        {/* Placeholder to maintain layout */}\n      </div>\n    );\n  }\n\n  const currentTheme = themeContext ? themeContext.theme : theme;\n\n  return (\n    <button\n      onClick={handleToggle}\n      className=\"p-2 rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-100 dark:text-gray-300 dark:hover:text-gray-100 dark:hover:bg-gray-700 transition-colors\"\n      aria-label={`Switch to ${currentTheme === 'light' ? 'dark' : 'light'} mode`}\n      title={`Switch to ${currentTheme === 'light' ? 'dark' : 'light'} mode`}\n    >\n      {currentTheme === 'light' ? (\n        // Moon icon for dark mode\n        <svg\n          className=\"w-5 h-5\"\n          fill=\"none\"\n          stroke=\"currentColor\"\n          viewBox=\"0 0 24 24\"\n          xmlns=\"http://www.w3.org/2000/svg\"\n        >\n          <path\n            strokeLinecap=\"round\"\n            strokeLinejoin=\"round\"\n            strokeWidth={2}\n            d=\"M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z\"\n          />\n        </svg>\n      ) : (\n        // Sun icon for light mode\n        <svg\n          className=\"w-5 h-5\"\n          fill=\"none\"\n          stroke=\"currentColor\"\n          viewBox=\"0 0 24 24\"\n          xmlns=\"http://www.w3.org/2000/svg\"\n        >\n          <path\n            strokeLinecap=\"round\"\n            strokeLinejoin=\"round\"\n            strokeWidth={2}\n            d=\"M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z\"\n          />\n        </svg>\n      )}\n    </button>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKe,SAAS;;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;IAErD,yDAAyD;IACzD,MAAM,eAAe,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD;IAEhC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,WAAW;YAEX,sCAAsC;YACtC,IAAI,CAAC,cAAc;gBACjB,MAAM,aAAa,aAAa,OAAO,CAAC;gBACxC,IAAI,YAAY;oBACd,SAAS;oBACT,WAAW;gBACb,OAAO;oBACL,MAAM,oBAAoB,OAAO,UAAU,CAAC,gCAAgC,OAAO;oBACnF,MAAM,eAAe,oBAAoB,SAAS;oBAClD,SAAS;oBACT,WAAW;gBACb;YACF;QACF;gCAAG;QAAC;KAAa;IAEjB,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,SAAS,eAAe;QACrC,IAAI,aAAa,QAAQ;YACvB,KAAK,SAAS,CAAC,GAAG,CAAC;QACrB,OAAO;YACL,KAAK,SAAS,CAAC,MAAM,CAAC;QACxB;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,cAAc;YAChB,2BAA2B;YAC3B,aAAa,WAAW;QAC1B,OAAO;YACL,+BAA+B;YAC/B,MAAM,WAAW,UAAU,UAAU,SAAS;YAC9C,SAAS;YACT,WAAW;YACX,aAAa,OAAO,CAAC,SAAS;QAChC;IACF;IAEA,2DAA2D;IAC3D,IAAI,CAAC,SAAS;QACZ,qBACE,6LAAC;YAAI,WAAU;;;;;;IAInB;IAEA,MAAM,eAAe,eAAe,aAAa,KAAK,GAAG;IAEzD,qBACE,6LAAC;QACC,SAAS;QACT,WAAU;QACV,cAAY,CAAC,UAAU,EAAE,iBAAiB,UAAU,SAAS,QAAQ,KAAK,CAAC;QAC3E,OAAO,CAAC,UAAU,EAAE,iBAAiB,UAAU,SAAS,QAAQ,KAAK,CAAC;kBAErE,iBAAiB,UAChB,0BAA0B;sBAC1B,6LAAC;YACC,WAAU;YACV,MAAK;YACL,QAAO;YACP,SAAQ;YACR,OAAM;sBAEN,cAAA,6LAAC;gBACC,eAAc;gBACd,gBAAe;gBACf,aAAa;gBACb,GAAE;;;;;;;;;;mBAIN,0BAA0B;sBAC1B,6LAAC;YACC,WAAU;YACV,MAAK;YACL,QAAO;YACP,SAAQ;YACR,OAAM;sBAEN,cAAA,6LAAC;gBACC,eAAc;gBACd,gBAAe;gBACf,aAAa;gBACb,GAAE;;;;;;;;;;;;;;;;AAMd;GApGwB;;QAKD,4HAAA,CAAA,eAAY;;;KALX", "debugId": null}}, {"offset": {"line": 146, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/source/repos/ravihani/valtics/valtics-ai/components/TrialBanner.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { getTrialStatusMessage } from '@/lib/trial';\n\nexport default function TrialBanner() {\n  const { user } = useAuth();\n  const [isDismissed, setIsDismissed] = useState(false);\n\n  // Don't show banner if user is not a trial user, is admin, or banner is dismissed\n  if (!user || !user.isTrialUser || user.role === 'admin' || isDismissed) {\n    return null;\n  }\n\n  const { message, type, daysRemaining } = getTrialStatusMessage(user);\n\n  // Don't show banner if no message\n  if (!message) {\n    return null;\n  }\n\n  const getBannerStyles = () => {\n    switch (type) {\n      case 'error':\n        return 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800 text-red-800 dark:text-red-200';\n      case 'warning':\n        return 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800 text-yellow-800 dark:text-yellow-200';\n      default:\n        return 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800 text-blue-800 dark:text-blue-200';\n    }\n  };\n\n  const getButtonStyles = () => {\n    switch (type) {\n      case 'error':\n        return 'bg-red-600 dark:bg-red-700 hover:bg-red-700 dark:hover:bg-red-800 text-white';\n      case 'warning':\n        return 'bg-yellow-600 dark:bg-yellow-700 hover:bg-yellow-700 dark:hover:bg-yellow-800 text-white';\n      default:\n        return 'bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-800 text-white';\n    }\n  };\n\n  const getIconStyles = () => {\n    switch (type) {\n      case 'error':\n        return 'text-red-400 dark:text-red-300';\n      case 'warning':\n        return 'text-yellow-400 dark:text-yellow-300';\n      default:\n        return 'text-blue-400 dark:text-blue-300';\n    }\n  };\n\n  return (\n    <div className={`border-l-4 p-4 ${getBannerStyles()}`}>\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex items-center\">\n          <div className=\"flex-shrink-0\">\n            {type === 'error' ? (\n              <svg className={`h-5 w-5 ${getIconStyles()}`} viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\n              </svg>\n            ) : type === 'warning' ? (\n              <svg className={`h-5 w-5 ${getIconStyles()}`} viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                <path fillRule=\"evenodd\" d=\"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\n              </svg>\n            ) : (\n              <svg className={`h-5 w-5 ${getIconStyles()}`} viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\" clipRule=\"evenodd\" />\n              </svg>\n            )}\n          </div>\n          <div className=\"ml-3\">\n            <p className=\"text-sm font-medium\">\n              {message}\n            </p>\n          </div>\n        </div>\n        \n        <div className=\"flex items-center space-x-3\">\n          <Link\n            href=\"/pricing\"\n            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${getButtonStyles()}`}\n          >\n            Upgrade Now\n          </Link>\n          \n          <button\n            onClick={() => setIsDismissed(true)}\n            className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\n            aria-label=\"Dismiss banner\"\n          >\n            <svg className=\"h-5 w-5\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n              <path fillRule=\"evenodd\" d=\"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\" clipRule=\"evenodd\" />\n            </svg>\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n}\n\n// Compact version for navigation bar\nexport function TrialIndicator() {\n  const { user } = useAuth();\n\n  if (!user || !user.isTrialUser || user.role === 'admin') {\n    return null;\n  }\n\n  const { daysRemaining } = getTrialStatusMessage(user);\n\n  if (daysRemaining <= 0) {\n    return (\n      <Link\n        href=\"/pricing\"\n        className=\"px-3 py-1 bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 text-xs font-medium rounded-full hover:bg-red-200 dark:hover:bg-red-800 transition-colors\"\n      >\n        Trial Expired\n      </Link>\n    );\n  }\n\n  const getIndicatorStyles = () => {\n    if (daysRemaining <= 3) {\n      return 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 hover:bg-yellow-200 dark:hover:bg-yellow-800';\n    }\n    return 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 hover:bg-blue-200 dark:hover:bg-blue-800';\n  };\n\n  return (\n    <Link\n      href=\"/pricing\"\n      className={`px-3 py-1 text-xs font-medium rounded-full transition-colors ${getIndicatorStyles()}`}\n    >\n      {daysRemaining} day{daysRemaining === 1 ? '' : 's'} left\n    </Link>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAOe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,kFAAkF;IAClF,IAAI,CAAC,QAAQ,CAAC,KAAK,WAAW,IAAI,KAAK,IAAI,KAAK,WAAW,aAAa;QACtE,OAAO;IACT;IAEA,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,+GAAA,CAAA,wBAAqB,AAAD,EAAE;IAE/D,kCAAkC;IAClC,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,MAAM,kBAAkB;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,kBAAkB;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,eAAe,EAAE,mBAAmB;kBACnD,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACZ,SAAS,wBACR,6LAAC;gCAAI,WAAW,CAAC,QAAQ,EAAE,iBAAiB;gCAAE,SAAQ;gCAAY,MAAK;0CACrE,cAAA,6LAAC;oCAAK,UAAS;oCAAU,GAAE;oCAA0N,UAAS;;;;;;;;;;uCAE9P,SAAS,0BACX,6LAAC;gCAAI,WAAW,CAAC,QAAQ,EAAE,iBAAiB;gCAAE,SAAQ;gCAAY,MAAK;0CACrE,cAAA,6LAAC;oCAAK,UAAS;oCAAU,GAAE;oCAAoN,UAAS;;;;;;;;;;qDAG1P,6LAAC;gCAAI,WAAW,CAAC,QAAQ,EAAE,iBAAiB;gCAAE,SAAQ;gCAAY,MAAK;0CACrE,cAAA,6LAAC;oCAAK,UAAS;oCAAU,GAAE;oCAAmI,UAAS;;;;;;;;;;;;;;;;sCAI7K,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;0CACV;;;;;;;;;;;;;;;;;8BAKP,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,+JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAW,CAAC,2DAA2D,EAAE,mBAAmB;sCAC7F;;;;;;sCAID,6LAAC;4BACC,SAAS,IAAM,eAAe;4BAC9B,WAAU;4BACV,cAAW;sCAEX,cAAA,6LAAC;gCAAI,WAAU;gCAAU,SAAQ;gCAAY,MAAK;0CAChD,cAAA,6LAAC;oCAAK,UAAS;oCAAU,GAAE;oCAAqM,UAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOvP;GAhGwB;;QACL,2HAAA,CAAA,UAAO;;;KADF;AAmGjB,SAAS;;IACd,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,UAAO,AAAD;IAEvB,IAAI,CAAC,QAAQ,CAAC,KAAK,WAAW,IAAI,KAAK,IAAI,KAAK,SAAS;QACvD,OAAO;IACT;IAEA,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,+GAAA,CAAA,wBAAqB,AAAD,EAAE;IAEhD,IAAI,iBAAiB,GAAG;QACtB,qBACE,6LAAC,+JAAA,CAAA,UAAI;YACH,MAAK;YACL,WAAU;sBACX;;;;;;IAIL;IAEA,MAAM,qBAAqB;QACzB,IAAI,iBAAiB,GAAG;YACtB,OAAO;QACT;QACA,OAAO;IACT;IAEA,qBACE,6LAAC,+JAAA,CAAA,UAAI;QACH,MAAK;QACL,WAAW,CAAC,6DAA6D,EAAE,sBAAsB;;YAEhG;YAAc;YAAK,kBAAkB,IAAI,KAAK;YAAI;;;;;;;AAGzD;IAnCgB;;QACG,2HAAA,CAAA,UAAO;;;MADV", "debugId": null}}, {"offset": {"line": 413, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/source/repos/ravihani/valtics/valtics-ai/components/Navigation.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { useRouter } from 'next/navigation';\nimport ThemeToggle from './ThemeToggle';\nimport { TrialIndicator } from './TrialBanner';\n\ninterface NavigationProps {\n  title?: string;\n  showBackButton?: boolean;\n  backUrl?: string;\n  backText?: string;\n}\n\nexport default function Navigation({\n  title = 'VALTICS AI',\n  showBackButton = false,\n  backUrl = '/dashboard',\n  backText = '← Back to Dashboard'\n}: NavigationProps) {\n  const { user, logOut, isAdmin } = useAuth();\n  const router = useRouter();\n\n  const handleLogout = async () => {\n    try {\n      await logOut();\n      router.push('/');\n    } catch (error) {\n      console.error('Error logging out:', error);\n    }\n  };\n\n  if (!user) {\n    return null;\n  }\n\n  return (\n    <nav className=\"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between h-16\">\n          <div className=\"flex items-center\">\n            <Link href=\"/dashboard\" className=\"flex items-center space-x-3\">\n              <Image\n                src=\"/logo.png\"\n                alt=\"VALTICS AI Logo\"\n                width={32}\n                height={32}\n                className=\"w-8 h-8\"\n              />\n              <span className=\"text-xl font-semibold text-gray-900 dark:text-white\">\n                {title}\n              </span>\n            </Link>\n          </div>\n\n          <div className=\"flex items-center space-x-4\">\n            {showBackButton && (\n              <Link\n                href={backUrl}\n                className=\"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium\"\n              >\n                {backText}\n              </Link>\n            )}\n\n            {!showBackButton && (\n              <>\n                <Link\n                  href=\"/brands\"\n                  className=\"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium\"\n                >\n                  Brands\n                </Link>\n                <Link\n                  href=\"/templates\"\n                  className=\"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium\"\n                >\n                  Templates\n                </Link>\n                {isAdmin && (\n                  <Link\n                    href=\"/admin\"\n                    className=\"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium\"\n                  >\n                    Admin\n                  </Link>\n                )}\n                <Link\n                  href=\"/profile\"\n                  className=\"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium\"\n                >\n                  Profile\n                </Link>\n              </>\n            )}\n\n            {/* Trial Indicator */}\n            <TrialIndicator />\n\n            {/* Theme Toggle */}\n            <ThemeToggle />\n\n            <button\n              onClick={handleLogout}\n              className=\"bg-red-600 dark:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-red-700 dark:hover:bg-red-800\"\n            >\n              Logout\n            </button>\n          </div>\n        </div>\n      </div>\n    </nav>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AAgBe,SAAS,WAAW,EACjC,QAAQ,YAAY,EACpB,iBAAiB,KAAK,EACtB,UAAU,YAAY,EACtB,WAAW,qBAAqB,EAChB;;IAChB,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,UAAO,AAAD;IACxC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe;QACnB,IAAI;YACF,MAAM;YACN,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;QACtC;IACF;IAEA,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAa,WAAU;;8CAChC,6LAAC,gIAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,OAAO;oCACP,QAAQ;oCACR,WAAU;;;;;;8CAEZ,6LAAC;oCAAK,WAAU;8CACb;;;;;;;;;;;;;;;;;kCAKP,6LAAC;wBAAI,WAAU;;4BACZ,gCACC,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAM;gCACN,WAAU;0CAET;;;;;;4BAIJ,CAAC,gCACA;;kDACE,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;oCAGA,yBACC,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAIH,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;0CAOL,6LAAC,6HAAA,CAAA,iBAAc;;;;;0CAGf,6LAAC,6HAAA,CAAA,UAAW;;;;;0CAEZ,6LAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;GAnGwB;;QAMY,2HAAA,CAAA,UAAO;QAC1B,qIAAA,CAAA,YAAS;;;KAPF", "debugId": null}}, {"offset": {"line": 602, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/source/repos/ravihani/valtics/valtics-ai/components/TrialRestriction.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { useAuth } from '@/contexts/AuthContext';\n\ninterface TrialRestrictionProps {\n  children: React.ReactNode;\n  feature: string;\n  showUpgrade?: boolean;\n}\n\nexport default function TrialRestriction({ \n  children, \n  feature, \n  showUpgrade = true \n}: TrialRestrictionProps) {\n  const { user, canAccessPremiumFeatures } = useAuth();\n\n  // If user can access premium features, show the content\n  if (canAccessPremiumFeatures) {\n    return <>{children}</>;\n  }\n\n  // If user is not logged in, redirect to login\n  if (!user) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center\">\n        <div className=\"max-w-md w-full bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 text-center\">\n          <div className=\"text-gray-400 dark:text-gray-500 text-6xl mb-4\">🔒</div>\n          <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white mb-4\">\n            Authentication Required\n          </h2>\n          <p className=\"text-gray-600 dark:text-gray-300 mb-6\">\n            Please log in to access {feature}.\n          </p>\n          <div className=\"space-y-3\">\n            <Link\n              href=\"/login\"\n              className=\"w-full bg-blue-600 dark:bg-blue-700 text-white px-4 py-2 rounded-md font-medium hover:bg-blue-700 dark:hover:bg-blue-800 inline-block\"\n            >\n              Log In\n            </Link>\n            <Link\n              href=\"/register\"\n              className=\"w-full bg-gray-200 dark:bg-gray-600 text-gray-800 dark:text-gray-200 px-4 py-2 rounded-md font-medium hover:bg-gray-300 dark:hover:bg-gray-500 inline-block\"\n            >\n              Start Free Trial\n            </Link>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  // Show trial expired message\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center\">\n      <div className=\"max-w-md w-full bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 text-center\">\n        <div className=\"text-red-400 dark:text-red-300 text-6xl mb-4\">⏰</div>\n        <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white mb-4\">\n          Trial Expired\n        </h2>\n        <p className=\"text-gray-600 dark:text-gray-300 mb-6\">\n          Your free trial has ended. Upgrade to continue using {feature} and other premium features.\n        </p>\n        \n        {/* Trial benefits */}\n        <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-6\">\n          <h3 className=\"text-sm font-medium text-gray-900 dark:text-white mb-2\">\n            What you'll get with a subscription:\n          </h3>\n          <ul className=\"text-sm text-gray-600 dark:text-gray-300 space-y-1\">\n            <li>• Unlimited BVA creation</li>\n            <li>• Access to all templates</li>\n            <li>• Advanced analytics</li>\n            <li>• Priority support</li>\n            <li>• Export to multiple formats</li>\n          </ul>\n        </div>\n\n        <div className=\"space-y-3\">\n          {showUpgrade && (\n            <Link\n              href=\"/pricing\"\n              className=\"w-full bg-blue-600 dark:bg-blue-700 text-white px-4 py-2 rounded-md font-medium hover:bg-blue-700 dark:hover:bg-blue-800 inline-block\"\n            >\n              Upgrade Now\n            </Link>\n          )}\n          <Link\n            href=\"/dashboard\"\n            className=\"w-full bg-gray-200 dark:bg-gray-600 text-gray-800 dark:text-gray-200 px-4 py-2 rounded-md font-medium hover:bg-gray-300 dark:hover:bg-gray-500 inline-block\"\n          >\n            Back to Dashboard\n          </Link>\n        </div>\n      </div>\n    </div>\n  );\n}\n\n// Hook for checking trial access in components\nexport function useTrialAccess() {\n  const { user, canAccessPremiumFeatures } = useAuth();\n\n  return {\n    hasAccess: canAccessPremiumFeatures,\n    isTrialUser: user?.isTrialUser || false,\n    isTrialExpired: user?.trialExpired || false,\n    user\n  };\n}\n\n// Component for inline trial restrictions (smaller version)\nexport function InlineTrialRestriction({ \n  children, \n  feature \n}: { \n  children: React.ReactNode; \n  feature: string; \n}) {\n  const { canAccessPremiumFeatures } = useAuth();\n\n  if (canAccessPremiumFeatures) {\n    return <>{children}</>;\n  }\n\n  return (\n    <div className=\"bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4\">\n      <div className=\"flex items-center\">\n        <div className=\"flex-shrink-0\">\n          <svg className=\"h-5 w-5 text-yellow-400 dark:text-yellow-300\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n            <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\n          </svg>\n        </div>\n        <div className=\"ml-3 flex-1\">\n          <p className=\"text-sm text-yellow-800 dark:text-yellow-200\">\n            <strong>Trial Required:</strong> {feature} requires an active subscription or trial.\n          </p>\n        </div>\n        <div className=\"ml-4\">\n          <Link\n            href=\"/pricing\"\n            className=\"text-sm bg-yellow-600 dark:bg-yellow-700 text-white px-3 py-1 rounded-md hover:bg-yellow-700 dark:hover:bg-yellow-800\"\n          >\n            Upgrade\n          </Link>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;;;AAHA;;;AAWe,SAAS,iBAAiB,EACvC,QAAQ,EACR,OAAO,EACP,cAAc,IAAI,EACI;;IACtB,MAAM,EAAE,IAAI,EAAE,wBAAwB,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,UAAO,AAAD;IAEjD,wDAAwD;IACxD,IAAI,0BAA0B;QAC5B,qBAAO;sBAAG;;IACZ;IAEA,8CAA8C;IAC9C,IAAI,CAAC,MAAM;QACT,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCAAiD;;;;;;kCAChE,6LAAC;wBAAG,WAAU;kCAAwD;;;;;;kCAGtE,6LAAC;wBAAE,WAAU;;4BAAwC;4BAC1B;4BAAQ;;;;;;;kCAEnC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;IAOX;IAEA,6BAA6B;IAC7B,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BAA+C;;;;;;8BAC9D,6LAAC;oBAAG,WAAU;8BAAwD;;;;;;8BAGtE,6LAAC;oBAAE,WAAU;;wBAAwC;wBACG;wBAAQ;;;;;;;8BAIhE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAyD;;;;;;sCAGvE,6LAAC;4BAAG,WAAU;;8CACZ,6LAAC;8CAAG;;;;;;8CACJ,6LAAC;8CAAG;;;;;;8CACJ,6LAAC;8CAAG;;;;;;8CACJ,6LAAC;8CAAG;;;;;;8CACJ,6LAAC;8CAAG;;;;;;;;;;;;;;;;;;8BAIR,6LAAC;oBAAI,WAAU;;wBACZ,6BACC,6LAAC,+JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;sCACX;;;;;;sCAIH,6LAAC,+JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AAOX;GAxFwB;;QAKqB,2HAAA,CAAA,UAAO;;;KAL5B;AA2FjB,SAAS;;IACd,MAAM,EAAE,IAAI,EAAE,wBAAwB,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,UAAO,AAAD;IAEjD,OAAO;QACL,WAAW;QACX,aAAa,MAAM,eAAe;QAClC,gBAAgB,MAAM,gBAAgB;QACtC;IACF;AACF;IATgB;;QAC6B,2HAAA,CAAA,UAAO;;;AAW7C,SAAS,uBAAuB,EACrC,QAAQ,EACR,OAAO,EAIR;;IACC,MAAM,EAAE,wBAAwB,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,UAAO,AAAD;IAE3C,IAAI,0BAA0B;QAC5B,qBAAO;sBAAG;;IACZ;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;wBAA+C,SAAQ;wBAAY,MAAK;kCACrF,cAAA,6LAAC;4BAAK,UAAS;4BAAU,GAAE;4BAA0N,UAAS;;;;;;;;;;;;;;;;8BAGlQ,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;;0CACX,6LAAC;0CAAO;;;;;;4BAAwB;4BAAE;4BAAQ;;;;;;;;;;;;8BAG9C,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;kCACX;;;;;;;;;;;;;;;;;;;;;;AAOX;IArCgB;;QAOuB,2HAAA,CAAA,UAAO;;;MAP9B", "debugId": null}}, {"offset": {"line": 963, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/source/repos/ravihani/valtics/valtics-ai/app/bva/new/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState, Suspense } from 'react';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { useRouter, useSearchParams } from 'next/navigation';\nimport Link from 'next/link';\nimport { doc, getDoc, collection, addDoc } from 'firebase/firestore';\nimport { db } from '@/lib/firebase/config';\nimport { Template, Brand, BVAFormData } from '@/types';\nimport Navigation from '@/components/Navigation';\nimport TrialRestriction from '@/components/TrialRestriction';\n\nfunction NewBVAContent() {\n  const { user, firebaseUser, loading } = useAuth();\n  const router = useRouter();\n  const searchParams = useSearchParams();\n  const templateId = searchParams.get('template');\n\n  const [template, setTemplate] = useState<Template | null>(null);\n  const [brand, setBrand] = useState<Brand | null>(null);\n  const [loadingData, setLoadingData] = useState(true);\n  const [currentStep, setCurrentStep] = useState(1);\n  const [formData, setFormData] = useState<BVAFormData>({\n    clientName: '',\n    projectName: '',\n    industry: '',\n    currentCosts: 0,\n    expectedBenefits: 0,\n    timeframe: 12,\n    additionalData: {}\n  });\n  const [saving, setSaving] = useState(false);\n\n  const totalSteps = 4;\n\n  useEffect(() => {\n    if (!loading && !user) {\n      router.push('/login');\n    }\n  }, [user, loading, router]);\n\n  useEffect(() => {\n    if (user && templateId) {\n      fetchTemplateData();\n    } else if (user && !templateId) {\n      setLoadingData(false);\n    }\n  }, [user, templateId]);\n\n  const fetchTemplateData = async () => {\n    try {\n      setLoadingData(true);\n\n      if (templateId) {\n        // Fetch template\n        const templateDoc = await getDoc(doc(db, 'templates', templateId));\n        if (templateDoc.exists()) {\n          const templateData = { id: templateDoc.id, ...templateDoc.data() } as Template;\n          setTemplate(templateData);\n\n          // Fetch brand\n          const brandDoc = await getDoc(doc(db, 'brands', templateData.brandId));\n          if (brandDoc.exists()) {\n            setBrand({ id: brandDoc.id, ...brandDoc.data() } as Brand);\n          }\n        }\n      }\n\n    } catch (error) {\n      console.error('Error fetching template data:', error);\n    } finally {\n      setLoadingData(false);\n    }\n  };\n\n  const handleInputChange = (field: keyof BVAFormData, value: any) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n\n  const handleNext = () => {\n    if (currentStep < totalSteps) {\n      setCurrentStep(currentStep + 1);\n    }\n  };\n\n  const handlePrevious = () => {\n    if (currentStep > 1) {\n      setCurrentStep(currentStep - 1);\n    }\n  };\n\n  const handleSave = async (status: 'draft' | 'in-progress' = 'draft') => {\n    if (!firebaseUser) return;\n\n    try {\n      setSaving(true);\n\n      const bvaData = {\n        userId: firebaseUser.uid,\n        templateId: templateId || '',\n        name: formData.projectName || 'Untitled BVA',\n        clientName: formData.clientName,\n        status,\n        data: formData,\n        createdAt: new Date(),\n        updatedAt: new Date()\n      };\n\n      const docRef = await addDoc(collection(db, 'bvaInstances'), bvaData);\n      router.push(`/bva/${docRef.id}`);\n\n    } catch (error) {\n      console.error('Error saving BVA:', error);\n      alert('Error saving BVA. Please try again.');\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  if (loading || loadingData) {\n    return (\n      <div className=\"flex min-h-screen items-center justify-center bg-gray-50 dark:bg-gray-900\">\n        <div className=\"text-xl text-gray-900 dark:text-white\">Loading...</div>\n      </div>\n    );\n  }\n\n  if (!user) {\n    return null;\n  }\n\n  const renderStep = () => {\n    switch (currentStep) {\n      case 1:\n        return (\n          <div className=\"space-y-6\">\n            <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white\">Project Information</h2>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                Project Name *\n              </label>\n              <input\n                type=\"text\"\n                value={formData.projectName}\n                onChange={(e) => handleInputChange('projectName', e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500\"\n                placeholder=\"Enter project name\"\n                required\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                Client Name *\n              </label>\n              <input\n                type=\"text\"\n                value={formData.clientName}\n                onChange={(e) => handleInputChange('clientName', e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500\"\n                placeholder=\"Enter client name\"\n                required\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                Industry\n              </label>\n              <select\n                value={formData.industry}\n                onChange={(e) => handleInputChange('industry', e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500\"\n              >\n                <option value=\"\">Select industry</option>\n                <option value=\"Technology\">Technology</option>\n                <option value=\"Healthcare\">Healthcare</option>\n                <option value=\"Finance\">Finance</option>\n                <option value=\"Manufacturing\">Manufacturing</option>\n                <option value=\"Retail\">Retail</option>\n                <option value=\"Education\">Education</option>\n                <option value=\"Government\">Government</option>\n                <option value=\"Other\">Other</option>\n              </select>\n            </div>\n          </div>\n        );\n\n      case 2:\n        return (\n          <div className=\"space-y-6\">\n            <h2 className=\"text-2xl font-bold text-gray-900\">Financial Information</h2>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Current Annual Costs ($)\n              </label>\n              <input\n                type=\"number\"\n                value={formData.currentCosts}\n                onChange={(e) => handleInputChange('currentCosts', parseFloat(e.target.value) || 0)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500\"\n                placeholder=\"0\"\n                min=\"0\"\n              />\n              <p className=\"text-sm text-gray-500 mt-1\">\n                Current annual costs related to this solution area\n              </p>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Expected Annual Benefits ($)\n              </label>\n              <input\n                type=\"number\"\n                value={formData.expectedBenefits}\n                onChange={(e) => handleInputChange('expectedBenefits', parseFloat(e.target.value) || 0)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500\"\n                placeholder=\"0\"\n                min=\"0\"\n              />\n              <p className=\"text-sm text-gray-500 mt-1\">\n                Expected annual benefits from the new solution\n              </p>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Analysis Timeframe (months)\n              </label>\n              <select\n                value={formData.timeframe}\n                onChange={(e) => handleInputChange('timeframe', parseInt(e.target.value))}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500\"\n              >\n                <option value={12}>12 months</option>\n                <option value={24}>24 months</option>\n                <option value={36}>36 months</option>\n                <option value={48}>48 months</option>\n                <option value={60}>60 months</option>\n              </select>\n            </div>\n          </div>\n        );\n\n      case 3:\n        return (\n          <div className=\"space-y-6\">\n            <h2 className=\"text-2xl font-bold text-gray-900\">Additional Details</h2>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Key Business Drivers\n              </label>\n              <textarea\n                value={formData.additionalData.businessDrivers || ''}\n                onChange={(e) => handleInputChange('additionalData', {\n                  ...formData.additionalData,\n                  businessDrivers: e.target.value\n                })}\n                rows={4}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500\"\n                placeholder=\"Describe the key business drivers for this project...\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Success Metrics\n              </label>\n              <textarea\n                value={formData.additionalData.successMetrics || ''}\n                onChange={(e) => handleInputChange('additionalData', {\n                  ...formData.additionalData,\n                  successMetrics: e.target.value\n                })}\n                rows={4}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500\"\n                placeholder=\"Define how success will be measured...\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Implementation Timeline\n              </label>\n              <input\n                type=\"text\"\n                value={formData.additionalData.timeline || ''}\n                onChange={(e) => handleInputChange('additionalData', {\n                  ...formData.additionalData,\n                  timeline: e.target.value\n                })}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500\"\n                placeholder=\"e.g., 6 months implementation, 3 months rollout\"\n              />\n            </div>\n          </div>\n        );\n\n      case 4:\n        return (\n          <div className=\"space-y-6\">\n            <h2 className=\"text-2xl font-bold text-gray-900\">Review & Save</h2>\n\n            <div className=\"bg-gray-50 rounded-lg p-6\">\n              <h3 className=\"text-lg font-medium text-gray-900 mb-4\">BVA Summary</h3>\n\n              <dl className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div>\n                  <dt className=\"text-sm font-medium text-gray-500\">Project Name</dt>\n                  <dd className=\"text-sm text-gray-900\">{formData.projectName}</dd>\n                </div>\n                <div>\n                  <dt className=\"text-sm font-medium text-gray-500\">Client</dt>\n                  <dd className=\"text-sm text-gray-900\">{formData.clientName}</dd>\n                </div>\n                <div>\n                  <dt className=\"text-sm font-medium text-gray-500\">Industry</dt>\n                  <dd className=\"text-sm text-gray-900\">{formData.industry || 'Not specified'}</dd>\n                </div>\n                <div>\n                  <dt className=\"text-sm font-medium text-gray-500\">Timeframe</dt>\n                  <dd className=\"text-sm text-gray-900\">{formData.timeframe} months</dd>\n                </div>\n                <div>\n                  <dt className=\"text-sm font-medium text-gray-500\">Current Costs</dt>\n                  <dd className=\"text-sm text-gray-900\">${formData.currentCosts.toLocaleString()}</dd>\n                </div>\n                <div>\n                  <dt className=\"text-sm font-medium text-gray-500\">Expected Benefits</dt>\n                  <dd className=\"text-sm text-gray-900\">${formData.expectedBenefits.toLocaleString()}</dd>\n                </div>\n              </dl>\n\n              {template && (\n                <div className=\"mt-4 pt-4 border-t\">\n                  <dt className=\"text-sm font-medium text-gray-500\">Template</dt>\n                  <dd className=\"text-sm text-gray-900\">{template.name}</dd>\n                </div>\n              )}\n            </div>\n\n            <div className=\"flex space-x-4\">\n              <button\n                onClick={() => handleSave('draft')}\n                disabled={saving}\n                className=\"flex-1 bg-gray-600 text-white px-6 py-3 rounded-md font-medium hover:bg-gray-700 disabled:opacity-50\"\n              >\n                {saving ? 'Saving...' : 'Save as Draft'}\n              </button>\n              <button\n                onClick={() => handleSave('in-progress')}\n                disabled={saving}\n                className=\"flex-1 bg-blue-600 text-white px-6 py-3 rounded-md font-medium hover:bg-blue-700 disabled:opacity-50\"\n              >\n                {saving ? 'Saving...' : 'Save & Continue'}\n              </button>\n            </div>\n          </div>\n        );\n\n      default:\n        return null;\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900\">\n      <Navigation title=\"VALTICS AI\" showBackButton={true} />\n\n      {/* Main Content */}\n      <div className=\"max-w-3xl mx-auto py-6 sm:px-6 lg:px-8\">\n        <div className=\"px-4 py-6 sm:px-0\">\n          {/* Header */}\n          <div className=\"mb-8\">\n            <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white\">Create New BVA</h1>\n            {template && brand && (\n              <p className=\"mt-2 text-gray-600 dark:text-gray-300\">\n                Using template: <span className=\"font-medium\">{template.name}</span> by {brand.name}\n              </p>\n            )}\n          </div>\n\n          {/* Progress Bar */}\n          <div className=\"mb-8\">\n            <div className=\"flex items-center\">\n              {Array.from({ length: totalSteps }, (_, i) => (\n                <div key={i} className=\"flex items-center\">\n                  <div\n                    className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${\n                      i + 1 <= currentStep\n                        ? 'bg-blue-600 dark:bg-blue-700 text-white'\n                        : 'bg-gray-200 dark:bg-gray-600 text-gray-600 dark:text-gray-300'\n                    }`}\n                  >\n                    {i + 1}\n                  </div>\n                  {i < totalSteps - 1 && (\n                    <div\n                      className={`w-16 h-1 mx-2 ${\n                        i + 1 < currentStep ? 'bg-blue-600 dark:bg-blue-700' : 'bg-gray-200 dark:bg-gray-600'\n                      }`}\n                    />\n                  )}\n                </div>\n              ))}\n            </div>\n            <div className=\"flex justify-between mt-2 text-sm text-gray-600 dark:text-gray-400\">\n              <span>Project Info</span>\n              <span>Financial</span>\n              <span>Details</span>\n              <span>Review</span>\n            </div>\n          </div>\n\n          {/* Form Content */}\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-md p-8 mb-8\">\n            {renderStep()}\n          </div>\n\n          {/* Navigation Buttons */}\n          {currentStep < totalSteps && (\n            <div className=\"flex justify-between\">\n              <button\n                onClick={handlePrevious}\n                disabled={currentStep === 1}\n                className=\"px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\"\n              >\n                Previous\n              </button>\n              <button\n                onClick={handleNext}\n                disabled={\n                  (currentStep === 1 && (!formData.projectName || !formData.clientName)) ||\n                  saving\n                }\n                className=\"px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed\"\n              >\n                Next\n              </button>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n\nexport default function NewBVA() {\n  return (\n    <Suspense fallback={\n      <div className=\"flex min-h-screen items-center justify-center\">\n        <div className=\"text-xl\">Loading...</div>\n      </div>\n    }>\n      <TrialRestriction feature=\"BVA Creation\">\n        <NewBVAContent />\n      </TrialRestriction>\n    </Suspense>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AAAA;AACA;AAEA;AACA;;;AAVA;;;;;;;;AAYA,SAAS;;IACP,MAAM,EAAE,IAAI,EAAE,YAAY,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,UAAO,AAAD;IAC9C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,aAAa,aAAa,GAAG,CAAC;IAEpC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;IAC1D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;QACpD,YAAY;QACZ,aAAa;QACb,UAAU;QACV,cAAc;QACd,kBAAkB;QAClB,WAAW;QACX,gBAAgB,CAAC;IACnB;IACA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,aAAa;IAEnB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,CAAC,WAAW,CAAC,MAAM;gBACrB,OAAO,IAAI,CAAC;YACd;QACF;kCAAG;QAAC;QAAM;QAAS;KAAO;IAE1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,QAAQ,YAAY;gBACtB;YACF,OAAO,IAAI,QAAQ,CAAC,YAAY;gBAC9B,eAAe;YACjB;QACF;kCAAG;QAAC;QAAM;KAAW;IAErB,MAAM,oBAAoB;QACxB,IAAI;YACF,eAAe;YAEf,IAAI,YAAY;gBACd,iBAAiB;gBACjB,MAAM,cAAc,MAAM,CAAA,GAAA,sKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,4HAAA,CAAA,KAAE,EAAE,aAAa;gBACtD,IAAI,YAAY,MAAM,IAAI;oBACxB,MAAM,eAAe;wBAAE,IAAI,YAAY,EAAE;wBAAE,GAAG,YAAY,IAAI,EAAE;oBAAC;oBACjE,YAAY;oBAEZ,cAAc;oBACd,MAAM,WAAW,MAAM,CAAA,GAAA,sKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,4HAAA,CAAA,KAAE,EAAE,UAAU,aAAa,OAAO;oBACpE,IAAI,SAAS,MAAM,IAAI;wBACrB,SAAS;4BAAE,IAAI,SAAS,EAAE;4BAAE,GAAG,SAAS,IAAI,EAAE;wBAAC;oBACjD;gBACF;YACF;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;QACjD,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,oBAAoB,CAAC,OAA0B;QACnD,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,MAAM,EAAE;YACX,CAAC;IACH;IAEA,MAAM,aAAa;QACjB,IAAI,cAAc,YAAY;YAC5B,eAAe,cAAc;QAC/B;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,cAAc,GAAG;YACnB,eAAe,cAAc;QAC/B;IACF;IAEA,MAAM,aAAa,OAAO,SAAkC,OAAO;QACjE,IAAI,CAAC,cAAc;QAEnB,IAAI;YACF,UAAU;YAEV,MAAM,UAAU;gBACd,QAAQ,aAAa,GAAG;gBACxB,YAAY,cAAc;gBAC1B,MAAM,SAAS,WAAW,IAAI;gBAC9B,YAAY,SAAS,UAAU;gBAC/B;gBACA,MAAM;gBACN,WAAW,IAAI;gBACf,WAAW,IAAI;YACjB;YAEA,MAAM,SAAS,MAAM,CAAA,GAAA,sKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,sKAAA,CAAA,aAAU,AAAD,EAAE,4HAAA,CAAA,KAAE,EAAE,iBAAiB;YAC5D,OAAO,IAAI,CAAC,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;QAEjC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;YACnC,MAAM;QACR,SAAU;YACR,UAAU;QACZ;IACF;IAEA,IAAI,WAAW,aAAa;QAC1B,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BAAwC;;;;;;;;;;;IAG7D;IAEA,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,MAAM,aAAa;QACjB,OAAQ;YACN,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAmD;;;;;;sCAEjE,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAAkE;;;;;;8CAGnF,6LAAC;oCACC,MAAK;oCACL,OAAO,SAAS,WAAW;oCAC3B,UAAU,CAAC,IAAM,kBAAkB,eAAe,EAAE,MAAM,CAAC,KAAK;oCAChE,WAAU;oCACV,aAAY;oCACZ,QAAQ;;;;;;;;;;;;sCAIZ,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAAkE;;;;;;8CAGnF,6LAAC;oCACC,MAAK;oCACL,OAAO,SAAS,UAAU;oCAC1B,UAAU,CAAC,IAAM,kBAAkB,cAAc,EAAE,MAAM,CAAC,KAAK;oCAC/D,WAAU;oCACV,aAAY;oCACZ,QAAQ;;;;;;;;;;;;sCAIZ,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAAkE;;;;;;8CAGnF,6LAAC;oCACC,OAAO,SAAS,QAAQ;oCACxB,UAAU,CAAC,IAAM,kBAAkB,YAAY,EAAE,MAAM,CAAC,KAAK;oCAC7D,WAAU;;sDAEV,6LAAC;4CAAO,OAAM;sDAAG;;;;;;sDACjB,6LAAC;4CAAO,OAAM;sDAAa;;;;;;sDAC3B,6LAAC;4CAAO,OAAM;sDAAa;;;;;;sDAC3B,6LAAC;4CAAO,OAAM;sDAAU;;;;;;sDACxB,6LAAC;4CAAO,OAAM;sDAAgB;;;;;;sDAC9B,6LAAC;4CAAO,OAAM;sDAAS;;;;;;sDACvB,6LAAC;4CAAO,OAAM;sDAAY;;;;;;sDAC1B,6LAAC;4CAAO,OAAM;sDAAa;;;;;;sDAC3B,6LAAC;4CAAO,OAAM;sDAAQ;;;;;;;;;;;;;;;;;;;;;;;;YAMhC,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAmC;;;;;;sCAEjD,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCACC,MAAK;oCACL,OAAO,SAAS,YAAY;oCAC5B,UAAU,CAAC,IAAM,kBAAkB,gBAAgB,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;oCACjF,WAAU;oCACV,aAAY;oCACZ,KAAI;;;;;;8CAEN,6LAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;sCAK5C,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCACC,MAAK;oCACL,OAAO,SAAS,gBAAgB;oCAChC,UAAU,CAAC,IAAM,kBAAkB,oBAAoB,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;oCACrF,WAAU;oCACV,aAAY;oCACZ,KAAI;;;;;;8CAEN,6LAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;sCAK5C,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCACC,OAAO,SAAS,SAAS;oCACzB,UAAU,CAAC,IAAM,kBAAkB,aAAa,SAAS,EAAE,MAAM,CAAC,KAAK;oCACvE,WAAU;;sDAEV,6LAAC;4CAAO,OAAO;sDAAI;;;;;;sDACnB,6LAAC;4CAAO,OAAO;sDAAI;;;;;;sDACnB,6LAAC;4CAAO,OAAO;sDAAI;;;;;;sDACnB,6LAAC;4CAAO,OAAO;sDAAI;;;;;;sDACnB,6LAAC;4CAAO,OAAO;sDAAI;;;;;;;;;;;;;;;;;;;;;;;;YAM7B,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAmC;;;;;;sCAEjD,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCACC,OAAO,SAAS,cAAc,CAAC,eAAe,IAAI;oCAClD,UAAU,CAAC,IAAM,kBAAkB,kBAAkB;4CACnD,GAAG,SAAS,cAAc;4CAC1B,iBAAiB,EAAE,MAAM,CAAC,KAAK;wCACjC;oCACA,MAAM;oCACN,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAIhB,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCACC,OAAO,SAAS,cAAc,CAAC,cAAc,IAAI;oCACjD,UAAU,CAAC,IAAM,kBAAkB,kBAAkB;4CACnD,GAAG,SAAS,cAAc;4CAC1B,gBAAgB,EAAE,MAAM,CAAC,KAAK;wCAChC;oCACA,MAAM;oCACN,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAIhB,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCACC,MAAK;oCACL,OAAO,SAAS,cAAc,CAAC,QAAQ,IAAI;oCAC3C,UAAU,CAAC,IAAM,kBAAkB,kBAAkB;4CACnD,GAAG,SAAS,cAAc;4CAC1B,UAAU,EAAE,MAAM,CAAC,KAAK;wCAC1B;oCACA,WAAU;oCACV,aAAY;;;;;;;;;;;;;;;;;;YAMtB,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAmC;;;;;;sCAEjD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAyC;;;;;;8CAEvD,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAoC;;;;;;8DAClD,6LAAC;oDAAG,WAAU;8DAAyB,SAAS,WAAW;;;;;;;;;;;;sDAE7D,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAoC;;;;;;8DAClD,6LAAC;oDAAG,WAAU;8DAAyB,SAAS,UAAU;;;;;;;;;;;;sDAE5D,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAoC;;;;;;8DAClD,6LAAC;oDAAG,WAAU;8DAAyB,SAAS,QAAQ,IAAI;;;;;;;;;;;;sDAE9D,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAoC;;;;;;8DAClD,6LAAC;oDAAG,WAAU;;wDAAyB,SAAS,SAAS;wDAAC;;;;;;;;;;;;;sDAE5D,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAoC;;;;;;8DAClD,6LAAC;oDAAG,WAAU;;wDAAwB;wDAAE,SAAS,YAAY,CAAC,cAAc;;;;;;;;;;;;;sDAE9E,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAoC;;;;;;8DAClD,6LAAC;oDAAG,WAAU;;wDAAwB;wDAAE,SAAS,gBAAgB,CAAC,cAAc;;;;;;;;;;;;;;;;;;;gCAInF,0BACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAoC;;;;;;sDAClD,6LAAC;4CAAG,WAAU;sDAAyB,SAAS,IAAI;;;;;;;;;;;;;;;;;;sCAK1D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAM,WAAW;oCAC1B,UAAU;oCACV,WAAU;8CAET,SAAS,cAAc;;;;;;8CAE1B,6LAAC;oCACC,SAAS,IAAM,WAAW;oCAC1B,UAAU;oCACV,WAAU;8CAET,SAAS,cAAc;;;;;;;;;;;;;;;;;;YAMlC;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,4HAAA,CAAA,UAAU;gBAAC,OAAM;gBAAa,gBAAgB;;;;;;0BAG/C,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAmD;;;;;;gCAChE,YAAY,uBACX,6LAAC;oCAAE,WAAU;;wCAAwC;sDACnC,6LAAC;4CAAK,WAAU;sDAAe,SAAS,IAAI;;;;;;wCAAQ;wCAAK,MAAM,IAAI;;;;;;;;;;;;;sCAMzF,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACZ,MAAM,IAAI,CAAC;wCAAE,QAAQ;oCAAW,GAAG,CAAC,GAAG,kBACtC,6LAAC;4CAAY,WAAU;;8DACrB,6LAAC;oDACC,WAAW,CAAC,0EAA0E,EACpF,IAAI,KAAK,cACL,4CACA,iEACJ;8DAED,IAAI;;;;;;gDAEN,IAAI,aAAa,mBAChB,6LAAC;oDACC,WAAW,CAAC,cAAc,EACxB,IAAI,IAAI,cAAc,iCAAiC,gCACvD;;;;;;;2CAdE;;;;;;;;;;8CAoBd,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;sDAAK;;;;;;sDACN,6LAAC;sDAAK;;;;;;sDACN,6LAAC;sDAAK;;;;;;sDACN,6LAAC;sDAAK;;;;;;;;;;;;;;;;;;sCAKV,6LAAC;4BAAI,WAAU;sCACZ;;;;;;wBAIF,cAAc,4BACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS;oCACT,UAAU,gBAAgB;oCAC1B,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,SAAS;oCACT,UACE,AAAC,gBAAgB,KAAK,CAAC,CAAC,SAAS,WAAW,IAAI,CAAC,SAAS,UAAU,KACpE;oCAEF,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;GAxbS;;QACiC,2HAAA,CAAA,UAAO;QAChC,qIAAA,CAAA,YAAS;QACH,qIAAA,CAAA,kBAAe;;;KAH7B;AA0bM,SAAS;IACtB,qBACE,6LAAC,6JAAA,CAAA,WAAQ;QAAC,wBACR,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BAAU;;;;;;;;;;;kBAG3B,cAAA,6LAAC,kIAAA,CAAA,UAAgB;YAAC,SAAQ;sBACxB,cAAA,6LAAC;;;;;;;;;;;;;;;AAIT;MAZwB", "debugId": null}}]}