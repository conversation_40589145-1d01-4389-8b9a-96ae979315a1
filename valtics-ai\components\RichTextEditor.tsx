'use client';

import { useState, useEffect, Component, ReactNode } from 'react';
import dynamic from 'next/dynamic';
import SimpleRichTextEditor from './SimpleRichTextEditor';

// Error Boundary Component
interface ErrorBoundaryProps {
  children: ReactNode;
  onError: () => void;
}

interface ErrorBoundaryState {
  hasError: boolean;
}

class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(): ErrorBoundaryState {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: any) {
    console.error('ReactQuill Error:', error, errorInfo);
    this.props.onError();
  }

  render() {
    if (this.state.hasError) {
      return null; // Let parent handle fallback
    }

    return this.props.children;
  }
}

// Dynamically import ReactQuill to avoid SSR issues
const ReactQuill = dynamic(() => import('react-quill'), {
  ssr: false,
  loading: () => <div className="h-32 bg-gray-100 dark:bg-gray-700 rounded animate-pulse"></div>
});

interface RichTextEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
}

const RichTextEditor: React.FC<RichTextEditorProps> = ({
  value,
  onChange,
  placeholder = 'Enter text...',
  className = '',
  disabled = false
}) => {
  const [hasError, setHasError] = useState(false);
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  const modules = {
    toolbar: [
      [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
      ['bold', 'italic', 'underline', 'strike'],
      [{ 'list': 'ordered'}, { 'list': 'bullet' }],
      [{ 'indent': '-1'}, { 'indent': '+1' }],
      ['link'],
      [{ 'align': [] }],
      ['clean']
    ],
  };

  const formats = [
    'header',
    'bold', 'italic', 'underline', 'strike',
    'list', 'bullet', 'indent',
    'link', 'align'
  ];

  // If there's an error or we're not on client side, use simple editor
  if (hasError || !isClient) {
    return (
      <SimpleRichTextEditor
        value={value}
        onChange={onChange}
        placeholder={placeholder}
        className={className}
        disabled={disabled}
      />
    );
  }

  const handleQuillError = () => {
    console.warn('ReactQuill error detected, falling back to simple editor');
    setHasError(true);
  };

  return (
    <div className={`rich-text-editor ${className}`}>
      <div className="mb-2">
        <button
          type="button"
          onClick={() => setHasError(true)}
          className="text-xs text-blue-600 dark:text-blue-400 hover:underline"
        >
          Switch to Simple Editor
        </button>
      </div>
      <ErrorBoundary onError={handleQuillError}>
        <ReactQuill
          theme="snow"
          value={value}
          onChange={onChange}
          modules={modules}
          formats={formats}
          placeholder={placeholder}
          readOnly={disabled}
          style={{
            backgroundColor: disabled ? '#f9fafb' : 'white',
          }}
        />
      </ErrorBoundary>
      <style jsx global>{`
        .rich-text-editor .ql-editor {
          min-height: 150px;
        }
        .rich-text-editor .ql-toolbar {
          border-top: 1px solid #e5e7eb;
          border-left: 1px solid #e5e7eb;
          border-right: 1px solid #e5e7eb;
        }
        .rich-text-editor .ql-container {
          border-bottom: 1px solid #e5e7eb;
          border-left: 1px solid #e5e7eb;
          border-right: 1px solid #e5e7eb;
        }
        .dark .rich-text-editor .ql-toolbar {
          border-color: #4b5563;
          background-color: #374151;
        }
        .dark .rich-text-editor .ql-container {
          border-color: #4b5563;
          background-color: #1f2937;
        }
        .dark .rich-text-editor .ql-editor {
          color: #f9fafb;
        }
        .dark .rich-text-editor .ql-toolbar .ql-stroke {
          stroke: #9ca3af;
        }
        .dark .rich-text-editor .ql-toolbar .ql-fill {
          fill: #9ca3af;
        }
      `}</style>
    </div>
  );
};

export default RichTextEditor;
