{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/source/repos/ravihani/valtics/valtics-ai/components/ThemeToggle.tsx"], "sourcesContent": ["'use client';\n\nimport { useThemeSafe } from '@/contexts/ThemeContext';\nimport { useState, useEffect } from 'react';\n\nexport default function ThemeToggle() {\n  const [mounted, setMounted] = useState(false);\n  const [theme, setTheme] = useState<'light' | 'dark'>('light');\n\n  // Use safe version that returns null instead of throwing\n  const themeContext = useThemeSafe();\n\n  useEffect(() => {\n    setMounted(true);\n\n    // If no context, manage theme locally\n    if (!themeContext) {\n      const savedTheme = localStorage.getItem('theme') as 'light' | 'dark';\n      if (savedTheme) {\n        setTheme(savedTheme);\n        applyTheme(savedTheme);\n      } else {\n        const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\n        const initialTheme = systemPrefersDark ? 'dark' : 'light';\n        setTheme(initialTheme);\n        applyTheme(initialTheme);\n      }\n    }\n  }, [themeContext]);\n\n  const applyTheme = (newTheme: 'light' | 'dark') => {\n    const root = document.documentElement;\n    if (newTheme === 'dark') {\n      root.classList.add('dark');\n    } else {\n      root.classList.remove('dark');\n    }\n  };\n\n  const handleToggle = () => {\n    if (themeContext) {\n      // Use context if available\n      themeContext.toggleTheme();\n    } else {\n      // Handle locally if no context\n      const newTheme = theme === 'light' ? 'dark' : 'light';\n      setTheme(newTheme);\n      applyTheme(newTheme);\n      localStorage.setItem('theme', newTheme);\n    }\n  };\n\n  // Don't render until mounted to prevent hydration mismatch\n  if (!mounted) {\n    return (\n      <div className=\"p-2 w-9 h-9\">\n        {/* Placeholder to maintain layout */}\n      </div>\n    );\n  }\n\n  const currentTheme = themeContext ? themeContext.theme : theme;\n\n  return (\n    <button\n      onClick={handleToggle}\n      className=\"p-2 rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-100 dark:text-gray-300 dark:hover:text-gray-100 dark:hover:bg-gray-700 transition-colors\"\n      aria-label={`Switch to ${currentTheme === 'light' ? 'dark' : 'light'} mode`}\n      title={`Switch to ${currentTheme === 'light' ? 'dark' : 'light'} mode`}\n    >\n      {currentTheme === 'light' ? (\n        // Moon icon for dark mode\n        <svg\n          className=\"w-5 h-5\"\n          fill=\"none\"\n          stroke=\"currentColor\"\n          viewBox=\"0 0 24 24\"\n          xmlns=\"http://www.w3.org/2000/svg\"\n        >\n          <path\n            strokeLinecap=\"round\"\n            strokeLinejoin=\"round\"\n            strokeWidth={2}\n            d=\"M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z\"\n          />\n        </svg>\n      ) : (\n        // Sun icon for light mode\n        <svg\n          className=\"w-5 h-5\"\n          fill=\"none\"\n          stroke=\"currentColor\"\n          viewBox=\"0 0 24 24\"\n          xmlns=\"http://www.w3.org/2000/svg\"\n        >\n          <path\n            strokeLinecap=\"round\"\n            strokeLinejoin=\"round\"\n            strokeWidth={2}\n            d=\"M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z\"\n          />\n        </svg>\n      )}\n    </button>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;IAErD,yDAAyD;IACzD,MAAM,eAAe,CAAA,GAAA,yHAAA,CAAA,eAAY,AAAD;IAEhC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;QAEX,sCAAsC;QACtC,IAAI,CAAC,cAAc;YACjB,MAAM,aAAa,aAAa,OAAO,CAAC;YACxC,IAAI,YAAY;gBACd,SAAS;gBACT,WAAW;YACb,OAAO;gBACL,MAAM,oBAAoB,OAAO,UAAU,CAAC,gCAAgC,OAAO;gBACnF,MAAM,eAAe,oBAAoB,SAAS;gBAClD,SAAS;gBACT,WAAW;YACb;QACF;IACF,GAAG;QAAC;KAAa;IAEjB,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,SAAS,eAAe;QACrC,IAAI,aAAa,QAAQ;YACvB,KAAK,SAAS,CAAC,GAAG,CAAC;QACrB,OAAO;YACL,KAAK,SAAS,CAAC,MAAM,CAAC;QACxB;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,cAAc;YAChB,2BAA2B;YAC3B,aAAa,WAAW;QAC1B,OAAO;YACL,+BAA+B;YAC/B,MAAM,WAAW,UAAU,UAAU,SAAS;YAC9C,SAAS;YACT,WAAW;YACX,aAAa,OAAO,CAAC,SAAS;QAChC;IACF;IAEA,2DAA2D;IAC3D,IAAI,CAAC,SAAS;QACZ,qBACE,8OAAC;YAAI,WAAU;;;;;;IAInB;IAEA,MAAM,eAAe,eAAe,aAAa,KAAK,GAAG;IAEzD,qBACE,8OAAC;QACC,SAAS;QACT,WAAU;QACV,cAAY,CAAC,UAAU,EAAE,iBAAiB,UAAU,SAAS,QAAQ,KAAK,CAAC;QAC3E,OAAO,CAAC,UAAU,EAAE,iBAAiB,UAAU,SAAS,QAAQ,KAAK,CAAC;kBAErE,iBAAiB,UAChB,0BAA0B;sBAC1B,8OAAC;YACC,WAAU;YACV,MAAK;YACL,QAAO;YACP,SAAQ;YACR,OAAM;sBAEN,cAAA,8OAAC;gBACC,eAAc;gBACd,gBAAe;gBACf,aAAa;gBACb,GAAE;;;;;;;;;;mBAIN,0BAA0B;sBAC1B,8OAAC;YACC,WAAU;YACV,MAAK;YACL,QAAO;YACP,SAAQ;YACR,OAAM;sBAEN,cAAA,8OAAC;gBACC,eAAc;gBACd,gBAAe;gBACf,aAAa;gBACb,GAAE;;;;;;;;;;;;;;;;AAMd", "debugId": null}}, {"offset": {"line": 155, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/source/repos/ravihani/valtics/valtics-ai/components/TrialBanner.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { getTrialStatusMessage } from '@/lib/trial';\n\nexport default function TrialBanner() {\n  const { user } = useAuth();\n  const [isDismissed, setIsDismissed] = useState(false);\n\n  // Don't show banner if user is not a trial user, is admin, or banner is dismissed\n  if (!user || !user.isTrialUser || user.role === 'admin' || isDismissed) {\n    return null;\n  }\n\n  const { message, type, daysRemaining } = getTrialStatusMessage(user);\n\n  // Don't show banner if no message\n  if (!message) {\n    return null;\n  }\n\n  const getBannerStyles = () => {\n    switch (type) {\n      case 'error':\n        return 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800 text-red-800 dark:text-red-200';\n      case 'warning':\n        return 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800 text-yellow-800 dark:text-yellow-200';\n      default:\n        return 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800 text-blue-800 dark:text-blue-200';\n    }\n  };\n\n  const getButtonStyles = () => {\n    switch (type) {\n      case 'error':\n        return 'bg-red-600 dark:bg-red-700 hover:bg-red-700 dark:hover:bg-red-800 text-white';\n      case 'warning':\n        return 'bg-yellow-600 dark:bg-yellow-700 hover:bg-yellow-700 dark:hover:bg-yellow-800 text-white';\n      default:\n        return 'bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-800 text-white';\n    }\n  };\n\n  const getIconStyles = () => {\n    switch (type) {\n      case 'error':\n        return 'text-red-400 dark:text-red-300';\n      case 'warning':\n        return 'text-yellow-400 dark:text-yellow-300';\n      default:\n        return 'text-blue-400 dark:text-blue-300';\n    }\n  };\n\n  return (\n    <div className={`border-l-4 p-4 ${getBannerStyles()}`}>\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex items-center\">\n          <div className=\"flex-shrink-0\">\n            {type === 'error' ? (\n              <svg className={`h-5 w-5 ${getIconStyles()}`} viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\n              </svg>\n            ) : type === 'warning' ? (\n              <svg className={`h-5 w-5 ${getIconStyles()}`} viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                <path fillRule=\"evenodd\" d=\"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\n              </svg>\n            ) : (\n              <svg className={`h-5 w-5 ${getIconStyles()}`} viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\" clipRule=\"evenodd\" />\n              </svg>\n            )}\n          </div>\n          <div className=\"ml-3\">\n            <p className=\"text-sm font-medium\">\n              {message}\n            </p>\n          </div>\n        </div>\n        \n        <div className=\"flex items-center space-x-3\">\n          <Link\n            href=\"/pricing\"\n            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${getButtonStyles()}`}\n          >\n            Upgrade Now\n          </Link>\n          \n          <button\n            onClick={() => setIsDismissed(true)}\n            className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\n            aria-label=\"Dismiss banner\"\n          >\n            <svg className=\"h-5 w-5\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n              <path fillRule=\"evenodd\" d=\"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\" clipRule=\"evenodd\" />\n            </svg>\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n}\n\n// Compact version for navigation bar\nexport function TrialIndicator() {\n  const { user } = useAuth();\n\n  if (!user || !user.isTrialUser || user.role === 'admin') {\n    return null;\n  }\n\n  const { daysRemaining } = getTrialStatusMessage(user);\n\n  if (daysRemaining <= 0) {\n    return (\n      <Link\n        href=\"/pricing\"\n        className=\"px-3 py-1 bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 text-xs font-medium rounded-full hover:bg-red-200 dark:hover:bg-red-800 transition-colors\"\n      >\n        Trial Expired\n      </Link>\n    );\n  }\n\n  const getIndicatorStyles = () => {\n    if (daysRemaining <= 3) {\n      return 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 hover:bg-yellow-200 dark:hover:bg-yellow-800';\n    }\n    return 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 hover:bg-blue-200 dark:hover:bg-blue-800';\n  };\n\n  return (\n    <Link\n      href=\"/pricing\"\n      className={`px-3 py-1 text-xs font-medium rounded-full transition-colors ${getIndicatorStyles()}`}\n    >\n      {daysRemaining} day{daysRemaining === 1 ? '' : 's'} left\n    </Link>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,kFAAkF;IAClF,IAAI,CAAC,QAAQ,CAAC,KAAK,WAAW,IAAI,KAAK,IAAI,KAAK,WAAW,aAAa;QACtE,OAAO;IACT;IAEA,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,4GAAA,CAAA,wBAAqB,AAAD,EAAE;IAE/D,kCAAkC;IAClC,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,MAAM,kBAAkB;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBA<PERSON>,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,kBAAkB;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,eAAe,EAAE,mBAAmB;kBACnD,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACZ,SAAS,wBACR,8OAAC;gCAAI,WAAW,CAAC,QAAQ,EAAE,iBAAiB;gCAAE,SAAQ;gCAAY,MAAK;0CACrE,cAAA,8OAAC;oCAAK,UAAS;oCAAU,GAAE;oCAA0N,UAAS;;;;;;;;;;uCAE9P,SAAS,0BACX,8OAAC;gCAAI,WAAW,CAAC,QAAQ,EAAE,iBAAiB;gCAAE,SAAQ;gCAAY,MAAK;0CACrE,cAAA,8OAAC;oCAAK,UAAS;oCAAU,GAAE;oCAAoN,UAAS;;;;;;;;;;qDAG1P,8OAAC;gCAAI,WAAW,CAAC,QAAQ,EAAE,iBAAiB;gCAAE,SAAQ;gCAAY,MAAK;0CACrE,cAAA,8OAAC;oCAAK,UAAS;oCAAU,GAAE;oCAAmI,UAAS;;;;;;;;;;;;;;;;sCAI7K,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CACV;;;;;;;;;;;;;;;;;8BAKP,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAW,CAAC,2DAA2D,EAAE,mBAAmB;sCAC7F;;;;;;sCAID,8OAAC;4BACC,SAAS,IAAM,eAAe;4BAC9B,WAAU;4BACV,cAAW;sCAEX,cAAA,8OAAC;gCAAI,WAAU;gCAAU,SAAQ;gCAAY,MAAK;0CAChD,cAAA,8OAAC;oCAAK,UAAS;oCAAU,GAAE;oCAAqM,UAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOvP;AAGO,SAAS;IACd,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,UAAO,AAAD;IAEvB,IAAI,CAAC,QAAQ,CAAC,KAAK,WAAW,IAAI,KAAK,IAAI,KAAK,SAAS;QACvD,OAAO;IACT;IAEA,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,4GAAA,CAAA,wBAAqB,AAAD,EAAE;IAEhD,IAAI,iBAAiB,GAAG;QACtB,qBACE,8OAAC,4JAAA,CAAA,UAAI;YACH,MAAK;YACL,WAAU;sBACX;;;;;;IAIL;IAEA,MAAM,qBAAqB;QACzB,IAAI,iBAAiB,GAAG;YACtB,OAAO;QACT;QACA,OAAO;IACT;IAEA,qBACE,8OAAC,4JAAA,CAAA,UAAI;QACH,MAAK;QACL,WAAW,CAAC,6DAA6D,EAAE,sBAAsB;;YAEhG;YAAc;YAAK,kBAAkB,IAAI,KAAK;YAAI;;;;;;;AAGzD", "debugId": null}}, {"offset": {"line": 401, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/source/repos/ravihani/valtics/valtics-ai/components/Navigation.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { useRouter } from 'next/navigation';\nimport ThemeToggle from './ThemeToggle';\nimport { TrialIndicator } from './TrialBanner';\n\ninterface NavigationProps {\n  title?: string;\n  showBackButton?: boolean;\n  backUrl?: string;\n  backText?: string;\n}\n\nexport default function Navigation({\n  title = 'VALTICS AI',\n  showBackButton = false,\n  backUrl = '/dashboard',\n  backText = '← Back to Dashboard'\n}: NavigationProps) {\n  const { user, logOut, isAdmin } = useAuth();\n  const router = useRouter();\n\n  const handleLogout = async () => {\n    try {\n      await logOut();\n      router.push('/');\n    } catch (error) {\n      console.error('Error logging out:', error);\n    }\n  };\n\n  if (!user) {\n    return null;\n  }\n\n  return (\n    <nav className=\"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between h-16\">\n          <div className=\"flex items-center\">\n            <Link href=\"/dashboard\" className=\"flex items-center space-x-3\">\n              <Image\n                src=\"/logo.png\"\n                alt=\"VALTICS AI Logo\"\n                width={32}\n                height={32}\n                className=\"w-8 h-8\"\n              />\n              <span className=\"text-xl font-semibold text-gray-900 dark:text-white\">\n                {title}\n              </span>\n            </Link>\n          </div>\n\n          <div className=\"flex items-center space-x-4\">\n            {showBackButton && (\n              <Link\n                href={backUrl}\n                className=\"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium\"\n              >\n                {backText}\n              </Link>\n            )}\n\n            {!showBackButton && (\n              <>\n                <Link\n                  href=\"/brands\"\n                  className=\"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium\"\n                >\n                  Brands\n                </Link>\n                <Link\n                  href=\"/templates\"\n                  className=\"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium\"\n                >\n                  Templates\n                </Link>\n                {isAdmin && (\n                  <Link\n                    href=\"/admin\"\n                    className=\"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium\"\n                  >\n                    Admin\n                  </Link>\n                )}\n                <Link\n                  href=\"/profile\"\n                  className=\"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium\"\n                >\n                  Profile\n                </Link>\n              </>\n            )}\n\n            {/* Trial Indicator */}\n            <TrialIndicator />\n\n            {/* Theme Toggle */}\n            <ThemeToggle />\n\n            <button\n              onClick={handleLogout}\n              className=\"bg-red-600 dark:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-red-700 dark:hover:bg-red-800\"\n            >\n              Logout\n            </button>\n          </div>\n        </div>\n      </div>\n    </nav>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AAgBe,SAAS,WAAW,EACjC,QAAQ,YAAY,EACpB,iBAAiB,KAAK,EACtB,UAAU,YAAY,EACtB,WAAW,qBAAqB,EAChB;IAChB,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,UAAO,AAAD;IACxC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe;QACnB,IAAI;YACF,MAAM;YACN,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;QACtC;IACF;IAEA,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAa,WAAU;;8CAChC,8OAAC,6HAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,OAAO;oCACP,QAAQ;oCACR,WAAU;;;;;;8CAEZ,8OAAC;oCAAK,WAAU;8CACb;;;;;;;;;;;;;;;;;kCAKP,8OAAC;wBAAI,WAAU;;4BACZ,gCACC,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAM;gCACN,WAAU;0CAET;;;;;;4BAIJ,CAAC,gCACA;;kDACE,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;oCAGA,yBACC,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAIH,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;0CAOL,8OAAC,0HAAA,CAAA,iBAAc;;;;;0CAGf,8OAAC,0HAAA,CAAA,UAAW;;;;;0CAEZ,8OAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}, {"offset": {"line": 576, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/source/repos/ravihani/valtics/valtics-ai/components/SimpleRichTextEditor.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useRef } from 'react';\n\ninterface SimpleRichTextEditorProps {\n  value: string;\n  onChange: (value: string) => void;\n  placeholder?: string;\n  className?: string;\n  disabled?: boolean;\n}\n\nconst SimpleRichTextEditor: React.FC<SimpleRichTextEditorProps> = ({\n  value,\n  onChange,\n  placeholder = 'Enter text...',\n  className = '',\n  disabled = false\n}) => {\n  const editorRef = useRef<HTMLDivElement>(null);\n\n  const handleInput = () => {\n    if (editorRef.current) {\n      onChange(editorRef.current.innerHTML);\n    }\n  };\n\n  const formatText = (command: string, value?: string) => {\n    document.execCommand(command, false, value);\n    if (editorRef.current) {\n      editorRef.current.focus();\n      onChange(editorRef.current.innerHTML);\n    }\n  };\n\n  const insertList = (ordered: boolean) => {\n    const command = ordered ? 'insertOrderedList' : 'insertUnorderedList';\n    formatText(command);\n  };\n\n  return (\n    <div className={`simple-rich-text-editor ${className}`}>\n      {/* Toolbar */}\n      <div className=\"border border-gray-300 dark:border-gray-600 border-b-0 bg-gray-50 dark:bg-gray-700 p-2 flex flex-wrap gap-1 rounded-t-md\">\n        <button\n          type=\"button\"\n          onClick={() => formatText('bold')}\n          className=\"px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-200 dark:hover:bg-gray-600 focus:outline-none\"\n          disabled={disabled}\n        >\n          <strong>B</strong>\n        </button>\n        <button\n          type=\"button\"\n          onClick={() => formatText('italic')}\n          className=\"px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-200 dark:hover:bg-gray-600 focus:outline-none\"\n          disabled={disabled}\n        >\n          <em>I</em>\n        </button>\n        <button\n          type=\"button\"\n          onClick={() => formatText('underline')}\n          className=\"px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-200 dark:hover:bg-gray-600 focus:outline-none\"\n          disabled={disabled}\n        >\n          <u>U</u>\n        </button>\n        <div className=\"w-px bg-gray-300 dark:bg-gray-600 mx-1\"></div>\n        <button\n          type=\"button\"\n          onClick={() => insertList(false)}\n          className=\"px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-200 dark:hover:bg-gray-600 focus:outline-none\"\n          disabled={disabled}\n        >\n          • List\n        </button>\n        <button\n          type=\"button\"\n          onClick={() => insertList(true)}\n          className=\"px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-200 dark:hover:bg-gray-600 focus:outline-none\"\n          disabled={disabled}\n        >\n          1. List\n        </button>\n        <div className=\"w-px bg-gray-300 dark:bg-gray-600 mx-1\"></div>\n        <select\n          onChange={(e) => formatText('formatBlock', e.target.value)}\n          className=\"px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none\"\n          disabled={disabled}\n          defaultValue=\"\"\n        >\n          <option value=\"\">Normal</option>\n          <option value=\"h1\">Heading 1</option>\n          <option value=\"h2\">Heading 2</option>\n          <option value=\"h3\">Heading 3</option>\n          <option value=\"h4\">Heading 4</option>\n          <option value=\"h5\">Heading 5</option>\n          <option value=\"h6\">Heading 6</option>\n        </select>\n      </div>\n\n      {/* Editor */}\n      <div\n        ref={editorRef}\n        contentEditable={!disabled}\n        onInput={handleInput}\n        dangerouslySetInnerHTML={{ __html: value }}\n        className={`\n          min-h-[150px] p-3 border border-gray-300 dark:border-gray-600 rounded-b-md\n          bg-white dark:bg-gray-700 text-gray-900 dark:text-white\n          focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\n          ${disabled ? 'bg-gray-100 dark:bg-gray-800 cursor-not-allowed' : ''}\n        `}\n        style={{\n          backgroundColor: disabled ? '#f9fafb' : undefined,\n        }}\n        data-placeholder={placeholder}\n      />\n\n      <style jsx>{`\n        .simple-rich-text-editor [contenteditable]:empty:before {\n          content: attr(data-placeholder);\n          color: #9ca3af;\n          pointer-events: none;\n        }\n        .simple-rich-text-editor [contenteditable] {\n          outline: none;\n        }\n        .simple-rich-text-editor [contenteditable] h1 {\n          font-size: 2em;\n          font-weight: bold;\n          margin: 0.67em 0;\n        }\n        .simple-rich-text-editor [contenteditable] h2 {\n          font-size: 1.5em;\n          font-weight: bold;\n          margin: 0.75em 0;\n        }\n        .simple-rich-text-editor [contenteditable] h3 {\n          font-size: 1.17em;\n          font-weight: bold;\n          margin: 0.83em 0;\n        }\n        .simple-rich-text-editor [contenteditable] h4 {\n          font-size: 1em;\n          font-weight: bold;\n          margin: 1.12em 0;\n        }\n        .simple-rich-text-editor [contenteditable] h5 {\n          font-size: 0.83em;\n          font-weight: bold;\n          margin: 1.5em 0;\n        }\n        .simple-rich-text-editor [contenteditable] h6 {\n          font-size: 0.75em;\n          font-weight: bold;\n          margin: 1.67em 0;\n        }\n        .simple-rich-text-editor [contenteditable] ul {\n          list-style-type: disc;\n          margin: 1em 0;\n          padding-left: 2em;\n        }\n        .simple-rich-text-editor [contenteditable] ol {\n          list-style-type: decimal;\n          margin: 1em 0;\n          padding-left: 2em;\n        }\n        .simple-rich-text-editor [contenteditable] li {\n          margin: 0.5em 0;\n        }\n        .simple-rich-text-editor [contenteditable] p {\n          margin: 1em 0;\n        }\n      `}</style>\n    </div>\n  );\n};\n\nexport default SimpleRichTextEditor;\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;;AAYA,MAAM,uBAA4D,CAAC,EACjE,KAAK,EACL,QAAQ,EACR,cAAc,eAAe,EAC7B,YAAY,EAAE,EACd,WAAW,KAAK,EACjB;IACC,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAEzC,MAAM,cAAc;QAClB,IAAI,UAAU,OAAO,EAAE;YACrB,SAAS,UAAU,OAAO,CAAC,SAAS;QACtC;IACF;IAEA,MAAM,aAAa,CAAC,SAAiB;QACnC,SAAS,WAAW,CAAC,SAAS,OAAO;QACrC,IAAI,UAAU,OAAO,EAAE;YACrB,UAAU,OAAO,CAAC,KAAK;YACvB,SAAS,UAAU,OAAO,CAAC,SAAS;QACtC;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,UAAU,UAAU,sBAAsB;QAChD,WAAW;IACb;IAEA,qBACE,8OAAC;kDAAe,CAAC,wBAAwB,EAAE,WAAW;;0BAEpD,8OAAC;0DAAc;;kCACb,8OAAC;wBACC,MAAK;wBACL,SAAS,IAAM,WAAW;wBAE1B,UAAU;kEADA;kCAGV,cAAA,8OAAC;;sCAAO;;;;;;;;;;;kCAEV,8OAAC;wBACC,MAAK;wBACL,SAAS,IAAM,WAAW;wBAE1B,UAAU;kEADA;kCAGV,cAAA,8OAAC;;sCAAG;;;;;;;;;;;kCAEN,8OAAC;wBACC,MAAK;wBACL,SAAS,IAAM,WAAW;wBAE1B,UAAU;kEADA;kCAGV,cAAA,8OAAC;;sCAAE;;;;;;;;;;;kCAEL,8OAAC;kEAAc;;;;;;kCACf,8OAAC;wBACC,MAAK;wBACL,SAAS,IAAM,WAAW;wBAE1B,UAAU;kEADA;kCAEX;;;;;;kCAGD,8OAAC;wBACC,MAAK;wBACL,SAAS,IAAM,WAAW;wBAE1B,UAAU;kEADA;kCAEX;;;;;;kCAGD,8OAAC;kEAAc;;;;;;kCACf,8OAAC;wBACC,UAAU,CAAC,IAAM,WAAW,eAAe,EAAE,MAAM,CAAC,KAAK;wBAEzD,UAAU;wBACV,cAAa;kEAFH;;0CAIV,8OAAC;gCAAO,OAAM;;0CAAG;;;;;;0CACjB,8OAAC;gCAAO,OAAM;;0CAAK;;;;;;0CACnB,8OAAC;gCAAO,OAAM;;0CAAK;;;;;;0CACnB,8OAAC;gCAAO,OAAM;;0CAAK;;;;;;0CACnB,8OAAC;gCAAO,OAAM;;0CAAK;;;;;;0CACnB,8OAAC;gCAAO,OAAM;;0CAAK;;;;;;0CACnB,8OAAC;gCAAO,OAAM;;0CAAK;;;;;;;;;;;;;;;;;;0BAKvB,8OAAC;gBACC,KAAK;gBACL,iBAAiB,CAAC;gBAClB,SAAS;gBACT,yBAAyB;oBAAE,QAAQ;gBAAM;gBAOzC,OAAO;oBACL,iBAAiB,WAAW,YAAY;gBAC1C;gBACA,oBAAkB;0DATP,CAAC;;;;UAIV,EAAE,WAAW,oDAAoD,GAAG;QACtE,CAAC;;;;;;;;;;;;;;;;AAiET;uCAEe", "debugId": null}}, {"offset": {"line": 821, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/source/repos/ravihani/valtics/valtics-ai/components/RichTextEditor.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, Component, ReactNode } from 'react';\nimport dynamic from 'next/dynamic';\nimport SimpleRichTextEditor from './SimpleRichTextEditor';\n\n// Error Boundary Component\ninterface ErrorBoundaryProps {\n  children: ReactNode;\n  onError: () => void;\n}\n\ninterface ErrorBoundaryState {\n  hasError: boolean;\n}\n\nclass ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {\n  constructor(props: ErrorBoundaryProps) {\n    super(props);\n    this.state = { hasError: false };\n  }\n\n  static getDerivedStateFromError(): ErrorBoundaryState {\n    return { hasError: true };\n  }\n\n  componentDidCatch(error: Error, errorInfo: any) {\n    console.error('ReactQuill Error:', error, errorInfo);\n    this.props.onError();\n  }\n\n  render() {\n    if (this.state.hasError) {\n      return null; // Let parent handle fallback\n    }\n\n    return this.props.children;\n  }\n}\n\n// Dynamically import ReactQuill to avoid SSR issues\nconst ReactQuill = dynamic(() => import('react-quill'), {\n  ssr: false,\n  loading: () => <div className=\"h-32 bg-gray-100 dark:bg-gray-700 rounded animate-pulse\"></div>\n});\n\ninterface RichTextEditorProps {\n  value: string;\n  onChange: (value: string) => void;\n  placeholder?: string;\n  className?: string;\n  disabled?: boolean;\n}\n\nconst RichTextEditor: React.FC<RichTextEditorProps> = ({\n  value,\n  onChange,\n  placeholder = 'Enter text...',\n  className = '',\n  disabled = false\n}) => {\n  const [hasError, setHasError] = useState(false);\n  const [isClient, setIsClient] = useState(false);\n\n  useEffect(() => {\n    setIsClient(true);\n  }, []);\n\n  const modules = {\n    toolbar: [\n      [{ 'header': [1, 2, 3, 4, 5, 6, false] }],\n      ['bold', 'italic', 'underline', 'strike'],\n      [{ 'list': 'ordered'}, { 'list': 'bullet' }],\n      [{ 'indent': '-1'}, { 'indent': '+1' }],\n      ['link'],\n      [{ 'align': [] }],\n      ['clean']\n    ],\n  };\n\n  const formats = [\n    'header',\n    'bold', 'italic', 'underline', 'strike',\n    'list', 'bullet', 'indent',\n    'link', 'align'\n  ];\n\n  // If there's an error or we're not on client side, use simple editor\n  if (hasError || !isClient) {\n    return (\n      <SimpleRichTextEditor\n        value={value}\n        onChange={onChange}\n        placeholder={placeholder}\n        className={className}\n        disabled={disabled}\n      />\n    );\n  }\n\n  const handleQuillError = () => {\n    console.warn('ReactQuill error detected, falling back to simple editor');\n    setHasError(true);\n  };\n\n  return (\n    <div className={`rich-text-editor ${className}`}>\n      <div className=\"mb-2\">\n        <button\n          type=\"button\"\n          onClick={() => setHasError(true)}\n          className=\"text-xs text-blue-600 dark:text-blue-400 hover:underline\"\n        >\n          Switch to Simple Editor\n        </button>\n      </div>\n      <ErrorBoundary onError={handleQuillError}>\n        <ReactQuill\n          theme=\"snow\"\n          value={value}\n          onChange={onChange}\n          modules={modules}\n          formats={formats}\n          placeholder={placeholder}\n          readOnly={disabled}\n          style={{\n            backgroundColor: disabled ? '#f9fafb' : 'white',\n          }}\n        />\n      </ErrorBoundary>\n      <style jsx global>{`\n        .rich-text-editor .ql-editor {\n          min-height: 150px;\n        }\n        .rich-text-editor .ql-toolbar {\n          border-top: 1px solid #e5e7eb;\n          border-left: 1px solid #e5e7eb;\n          border-right: 1px solid #e5e7eb;\n        }\n        .rich-text-editor .ql-container {\n          border-bottom: 1px solid #e5e7eb;\n          border-left: 1px solid #e5e7eb;\n          border-right: 1px solid #e5e7eb;\n        }\n        .dark .rich-text-editor .ql-toolbar {\n          border-color: #4b5563;\n          background-color: #374151;\n        }\n        .dark .rich-text-editor .ql-container {\n          border-color: #4b5563;\n          background-color: #1f2937;\n        }\n        .dark .rich-text-editor .ql-editor {\n          color: #f9fafb;\n        }\n        .dark .rich-text-editor .ql-toolbar .ql-stroke {\n          stroke: #9ca3af;\n        }\n        .dark .rich-text-editor .ql-toolbar .ql-fill {\n          fill: #9ca3af;\n        }\n      `}</style>\n    </div>\n  );\n};\n\nexport default RichTextEditor;\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;;AAJA;;;;;;AAgBA,MAAM,sBAAsB,qMAAA,CAAA,YAAS;IACnC,YAAY,KAAyB,CAAE;QACrC,KAAK,CAAC;QACN,IAAI,CAAC,KAAK,GAAG;YAAE,UAAU;QAAM;IACjC;IAEA,OAAO,2BAA+C;QACpD,OAAO;YAAE,UAAU;QAAK;IAC1B;IAEA,kBAAkB,KAAY,EAAE,SAAc,EAAE;QAC9C,QAAQ,KAAK,CAAC,qBAAqB,OAAO;QAC1C,IAAI,CAAC,KAAK,CAAC,OAAO;IACpB;IAEA,SAAS;QACP,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;YACvB,OAAO,MAAM,6BAA6B;QAC5C;QAEA,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;IAC5B;AACF;AAEA,oDAAoD;AACpD,MAAM,aAAa,CAAA,GAAA,+JAAA,CAAA,UAAO,AAAD;;;;;;IACvB,KAAK;IACL,SAAS,kBAAM,8OAAC;YAAI,WAAU;;;;;;;AAWhC,MAAM,iBAAgD,CAAC,EACrD,KAAK,EACL,QAAQ,EACR,cAAc,eAAe,EAC7B,YAAY,EAAE,EACd,WAAW,KAAK,EACjB;IACC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,YAAY;IACd,GAAG,EAAE;IAEL,MAAM,UAAU;QACd,SAAS;YACP;gBAAC;oBAAE,UAAU;wBAAC;wBAAG;wBAAG;wBAAG;wBAAG;wBAAG;wBAAG;qBAAM;gBAAC;aAAE;YACzC;gBAAC;gBAAQ;gBAAU;gBAAa;aAAS;YACzC;gBAAC;oBAAE,QAAQ;gBAAS;gBAAG;oBAAE,QAAQ;gBAAS;aAAE;YAC5C;gBAAC;oBAAE,UAAU;gBAAI;gBAAG;oBAAE,UAAU;gBAAK;aAAE;YACvC;gBAAC;aAAO;YACR;gBAAC;oBAAE,SAAS,EAAE;gBAAC;aAAE;YACjB;gBAAC;aAAQ;SACV;IACH;IAEA,MAAM,UAAU;QACd;QACA;QAAQ;QAAU;QAAa;QAC/B;QAAQ;QAAU;QAClB;QAAQ;KACT;IAED,qEAAqE;IACrE,IAAI,YAAY,CAAC,UAAU;QACzB,qBACE,8OAAC,mIAAA,CAAA,UAAoB;YACnB,OAAO;YACP,UAAU;YACV,aAAa;YACb,WAAW;YACX,UAAU;;;;;;IAGhB;IAEA,MAAM,mBAAmB;QACvB,QAAQ,IAAI,CAAC;QACb,YAAY;IACd;IAEA,qBACE,8OAAC;kDAAe,CAAC,iBAAiB,EAAE,WAAW;;0BAC7C,8OAAC;0DAAc;0BACb,cAAA,8OAAC;oBACC,MAAK;oBACL,SAAS,IAAM,YAAY;8DACjB;8BACX;;;;;;;;;;;0BAIH,8OAAC;gBAAc,SAAS;0BACtB,cAAA,8OAAC;oBACC,OAAM;oBACN,OAAO;oBACP,UAAU;oBACV,SAAS;oBACT,SAAS;oBACT,aAAa;oBACb,UAAU;oBACV,OAAO;wBACL,iBAAiB,WAAW,YAAY;oBAC1C;;;;;;;;;;;;;;;;;;;;;AAqCV;uCAEe", "debugId": null}}, {"offset": {"line": 1022, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/source/repos/ravihani/valtics/valtics-ai/components/FileUpload.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useRef } from 'react';\n\ninterface FileUploadProps {\n  onFileSelect: (file: File) => void;\n  onFileRemove: () => void;\n  currentFileUrl?: string;\n  currentFileName?: string;\n  accept?: string;\n  maxSize?: number; // in MB\n  disabled?: boolean;\n  className?: string;\n}\n\nconst FileUpload: React.FC<FileUploadProps> = ({\n  onFileSelect,\n  onFileRemove,\n  currentFileUrl,\n  currentFileName,\n  accept = '.pdf,.doc,.docx,.txt,.jpg,.jpeg,.png',\n  maxSize = 10, // 10MB default\n  disabled = false,\n  className = ''\n}) => {\n  const [dragActive, setDragActive] = useState(false);\n  const [uploading, setUploading] = useState(false);\n  const fileInputRef = useRef<HTMLInputElement>(null);\n\n  const handleDrag = (e: React.DragEvent) => {\n    e.preventDefault();\n    e.stopPropagation();\n    if (e.type === 'dragenter' || e.type === 'dragover') {\n      setDragActive(true);\n    } else if (e.type === 'dragleave') {\n      setDragActive(false);\n    }\n  };\n\n  const handleDrop = (e: React.DragEvent) => {\n    e.preventDefault();\n    e.stopPropagation();\n    setDragActive(false);\n\n    if (disabled) return;\n\n    const files = e.dataTransfer.files;\n    if (files && files[0]) {\n      handleFileSelection(files[0]);\n    }\n  };\n\n  const handleFileSelection = (file: File) => {\n    // Check file size\n    if (file.size > maxSize * 1024 * 1024) {\n      alert(`File size must be less than ${maxSize}MB`);\n      return;\n    }\n\n    // Check file type\n    const allowedTypes = accept.split(',').map(type => type.trim());\n    const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();\n    const allowedMimeTypes = [\n      'application/pdf',\n      'application/msword',\n      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',\n      'text/plain',\n      'image/jpeg',\n      'image/jpg',\n      'image/png'\n    ];\n\n    const isValidType = allowedTypes.some(type =>\n      type === fileExtension ||\n      (type.includes('/') && file.type === type)\n    ) || allowedMimeTypes.includes(file.type);\n\n    if (!isValidType) {\n      alert(`Please select a valid file type: ${accept}`);\n      return;\n    }\n\n    onFileSelect(file);\n  };\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const files = e.target.files;\n    if (files && files[0]) {\n      handleFileSelection(files[0]);\n    }\n  };\n\n  const openFileDialog = () => {\n    if (!disabled && fileInputRef.current) {\n      fileInputRef.current.click();\n    }\n  };\n\n  const handleRemoveFile = () => {\n    if (fileInputRef.current) {\n      fileInputRef.current.value = '';\n    }\n    onFileRemove();\n  };\n\n  const isImageFile = (fileName: string) => {\n    const imageExtensions = ['.jpg', '.jpeg', '.png'];\n    const extension = '.' + fileName.split('.').pop()?.toLowerCase();\n    return imageExtensions.includes(extension);\n  };\n\n  return (\n    <div className={`file-upload ${className}`}>\n      <input\n        ref={fileInputRef}\n        type=\"file\"\n        accept={accept}\n        onChange={handleInputChange}\n        className=\"hidden\"\n        disabled={disabled}\n      />\n\n      {currentFileUrl || currentFileName ? (\n        // File already uploaded\n        <div className=\"border-2 border-dashed border-green-300 dark:border-green-600 rounded-lg p-4 bg-green-50 dark:bg-green-900/20\">\n          <div className=\"flex items-start justify-between\">\n            <div className=\"flex items-start space-x-3\">\n              <div className=\"flex-shrink-0\">\n                {currentFileName && isImageFile(currentFileName) && currentFileUrl ? (\n                  // Image preview\n                  <div className=\"w-16 h-16 rounded-lg overflow-hidden border border-gray-200 dark:border-gray-600\">\n                    <img\n                      src={currentFileUrl}\n                      alt={currentFileName}\n                      className=\"w-full h-full object-cover\"\n                      onError={(e) => {\n                        // Fallback to document icon if image fails to load\n                        const target = e.target as HTMLImageElement;\n                        target.style.display = 'none';\n                        target.nextElementSibling?.classList.remove('hidden');\n                      }}\n                    />\n                    <svg className=\"hidden h-16 w-16 text-green-600 dark:text-green-400 p-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                    </svg>\n                  </div>\n                ) : (\n                  // Document icon\n                  <svg className=\"h-8 w-8 text-green-600 dark:text-green-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                  </svg>\n                )}\n              </div>\n              <div>\n                <p className=\"text-sm font-medium text-green-800 dark:text-green-200\">\n                  {currentFileName || 'File uploaded'}\n                </p>\n                <p className=\"text-xs text-green-600 dark:text-green-400\">\n                  {currentFileName && isImageFile(currentFileName) ? 'Image uploaded successfully' : 'Document uploaded successfully'}\n                </p>\n              </div>\n            </div>\n            <div className=\"flex space-x-2\">\n              {currentFileUrl && (\n                <a\n                  href={currentFileUrl}\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  className=\"text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm font-medium\"\n                >\n                  View\n                </a>\n              )}\n              <button\n                type=\"button\"\n                onClick={handleRemoveFile}\n                disabled={disabled}\n                className=\"text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 text-sm font-medium disabled:opacity-50\"\n              >\n                Remove\n              </button>\n            </div>\n          </div>\n        </div>\n      ) : (\n        // Upload area\n        <div\n          className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${\n            dragActive\n              ? 'border-blue-400 bg-blue-50 dark:bg-blue-900/20'\n              : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'\n          } ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}\n          onDragEnter={handleDrag}\n          onDragLeave={handleDrag}\n          onDragOver={handleDrag}\n          onDrop={handleDrop}\n          onClick={openFileDialog}\n        >\n          <div className=\"space-y-2\">\n            <svg\n              className=\"mx-auto h-12 w-12 text-gray-400 dark:text-gray-500\"\n              stroke=\"currentColor\"\n              fill=\"none\"\n              viewBox=\"0 0 48 48\"\n            >\n              <path\n                d=\"M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02\"\n                strokeWidth={2}\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n              />\n            </svg>\n            <div className=\"text-sm text-gray-600 dark:text-gray-400\">\n              <span className=\"font-medium text-blue-600 dark:text-blue-400\">\n                Click to upload\n              </span>{' '}\n              or drag and drop\n            </div>\n            <p className=\"text-xs text-gray-500 dark:text-gray-400\">\n              Documents & Images ({accept.replace(/\\./g, '').toUpperCase()}) up to {maxSize}MB\n            </p>\n          </div>\n        </div>\n      )}\n\n      {uploading && (\n        <div className=\"mt-2 flex items-center space-x-2 text-sm text-blue-600 dark:text-blue-400\">\n          <svg className=\"animate-spin h-4 w-4\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n            <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n            <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n          </svg>\n          <span>Uploading...</span>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default FileUpload;\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAeA,MAAM,aAAwC,CAAC,EAC7C,YAAY,EACZ,YAAY,EACZ,cAAc,EACd,eAAe,EACf,SAAS,sCAAsC,EAC/C,UAAU,EAAE,EACZ,WAAW,KAAK,EAChB,YAAY,EAAE,EACf;IACC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,MAAM,aAAa,CAAC;QAClB,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,IAAI,EAAE,IAAI,KAAK,eAAe,EAAE,IAAI,KAAK,YAAY;YACnD,cAAc;QAChB,OAAO,IAAI,EAAE,IAAI,KAAK,aAAa;YACjC,cAAc;QAChB;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,cAAc;QAEd,IAAI,UAAU;QAEd,MAAM,QAAQ,EAAE,YAAY,CAAC,KAAK;QAClC,IAAI,SAAS,KAAK,CAAC,EAAE,EAAE;YACrB,oBAAoB,KAAK,CAAC,EAAE;QAC9B;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,kBAAkB;QAClB,IAAI,KAAK,IAAI,GAAG,UAAU,OAAO,MAAM;YACrC,MAAM,CAAC,4BAA4B,EAAE,QAAQ,EAAE,CAAC;YAChD;QACF;QAEA,kBAAkB;QAClB,MAAM,eAAe,OAAO,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI;QAC5D,MAAM,gBAAgB,MAAM,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI;QACxD,MAAM,mBAAmB;YACvB;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QAED,MAAM,cAAc,aAAa,IAAI,CAAC,CAAA,OACpC,SAAS,iBACR,KAAK,QAAQ,CAAC,QAAQ,KAAK,IAAI,KAAK,SAClC,iBAAiB,QAAQ,CAAC,KAAK,IAAI;QAExC,IAAI,CAAC,aAAa;YAChB,MAAM,CAAC,iCAAiC,EAAE,QAAQ;YAClD;QACF;QAEA,aAAa;IACf;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;QAC5B,IAAI,SAAS,KAAK,CAAC,EAAE,EAAE;YACrB,oBAAoB,KAAK,CAAC,EAAE;QAC9B;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,CAAC,YAAY,aAAa,OAAO,EAAE;YACrC,aAAa,OAAO,CAAC,KAAK;QAC5B;IACF;IAEA,MAAM,mBAAmB;QACvB,IAAI,aAAa,OAAO,EAAE;YACxB,aAAa,OAAO,CAAC,KAAK,GAAG;QAC/B;QACA;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,MAAM,kBAAkB;YAAC;YAAQ;YAAS;SAAO;QACjD,MAAM,YAAY,MAAM,SAAS,KAAK,CAAC,KAAK,GAAG,IAAI;QACnD,OAAO,gBAAgB,QAAQ,CAAC;IAClC;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,YAAY,EAAE,WAAW;;0BACxC,8OAAC;gBACC,KAAK;gBACL,MAAK;gBACL,QAAQ;gBACR,UAAU;gBACV,WAAU;gBACV,UAAU;;;;;;YAGX,kBAAkB,kBACjB,wBAAwB;0BACxB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACZ,mBAAmB,YAAY,oBAAoB,iBAClD,gBAAgB;kDAChB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,KAAK;gDACL,KAAK;gDACL,WAAU;gDACV,SAAS,CAAC;oDACR,mDAAmD;oDACnD,MAAM,SAAS,EAAE,MAAM;oDACvB,OAAO,KAAK,CAAC,OAAO,GAAG;oDACvB,OAAO,kBAAkB,EAAE,UAAU,OAAO;gDAC9C;;;;;;0DAEF,8OAAC;gDAAI,WAAU;gDAA0D,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACjH,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;+CAIzE,gBAAgB;kDAChB,8OAAC;wCAAI,WAAU;wCAA6C,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACpG,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;8CAI3E,8OAAC;;sDACC,8OAAC;4CAAE,WAAU;sDACV,mBAAmB;;;;;;sDAEtB,8OAAC;4CAAE,WAAU;sDACV,mBAAmB,YAAY,mBAAmB,gCAAgC;;;;;;;;;;;;;;;;;;sCAIzF,8OAAC;4BAAI,WAAU;;gCACZ,gCACC,8OAAC;oCACC,MAAM;oCACN,QAAO;oCACP,KAAI;oCACJ,WAAU;8CACX;;;;;;8CAIH,8OAAC;oCACC,MAAK;oCACL,SAAS;oCACT,UAAU;oCACV,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;uBAOP,cAAc;0BACd,8OAAC;gBACC,WAAW,CAAC,oEAAoE,EAC9E,aACI,mDACA,wFACL,CAAC,EAAE,WAAW,kCAAkC,kBAAkB;gBACnE,aAAa;gBACb,aAAa;gBACb,YAAY;gBACZ,QAAQ;gBACR,SAAS;0BAET,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,WAAU;4BACV,QAAO;4BACP,MAAK;4BACL,SAAQ;sCAER,cAAA,8OAAC;gCACC,GAAE;gCACF,aAAa;gCACb,eAAc;gCACd,gBAAe;;;;;;;;;;;sCAGnB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CAA+C;;;;;;gCAEvD;gCAAI;;;;;;;sCAGd,8OAAC;4BAAE,WAAU;;gCAA2C;gCACjC,OAAO,OAAO,CAAC,OAAO,IAAI,WAAW;gCAAG;gCAAS;gCAAQ;;;;;;;;;;;;;;;;;;YAMrF,2BACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;wBAAuB,OAAM;wBAA6B,MAAK;wBAAO,SAAQ;;0CAC3F,8OAAC;gCAAO,WAAU;gCAAa,IAAG;gCAAK,IAAG;gCAAK,GAAE;gCAAK,QAAO;gCAAe,aAAY;;;;;;0CACxF,8OAAC;gCAAK,WAAU;gCAAa,MAAK;gCAAe,GAAE;;;;;;;;;;;;kCAErD,8OAAC;kCAAK;;;;;;;;;;;;;;;;;;AAKhB;uCAEe", "debugId": null}}, {"offset": {"line": 1409, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/source/repos/ravihani/valtics/valtics-ai/lib/firebase/storage.ts"], "sourcesContent": ["import { storage } from './config';\nimport { ref, uploadBytes, getDownloadURL, deleteObject } from 'firebase/storage';\n\nexport class StorageService {\n  // Upload a file to Firebase Storage\n  static async uploadFile(\n    file: File,\n    path: string,\n    onProgress?: (progress: number) => void\n  ): Promise<string> {\n    try {\n      // Create a reference to the file location\n      const storageRef = ref(storage, path);\n\n      // Upload the file\n      const snapshot = await uploadBytes(storageRef, file);\n\n      // Get the download URL\n      const downloadURL = await getDownloadURL(snapshot.ref);\n\n      return downloadURL;\n    } catch (error) {\n      console.error('Error uploading file:', error);\n      throw new Error('Failed to upload file');\n    }\n  }\n\n  // Upload template document\n  static async uploadTemplateDocument(\n    file: File,\n    templateId: string,\n    documentType: 'enterpriseNeed' | 'solutionDescription' | 'riskOfNoInvestment'\n  ): Promise<string> {\n    const timestamp = Date.now();\n    const fileExtension = file.name.split('.').pop();\n    const fileName = `${documentType}_${timestamp}.${fileExtension}`;\n    const path = `templates/${templateId}/documents/${fileName}`;\n\n    return this.uploadFile(file, path);\n  }\n\n  // Delete a file from Firebase Storage\n  static async deleteFile(url: string): Promise<void> {\n    try {\n      // Extract the path from the URL\n      const urlParts = url.split('/');\n      const pathIndex = urlParts.findIndex(part => part === 'o') + 1;\n      if (pathIndex === 0) {\n        throw new Error('Invalid Firebase Storage URL');\n      }\n\n      const encodedPath = urlParts[pathIndex].split('?')[0];\n      const path = decodeURIComponent(encodedPath);\n\n      const storageRef = ref(storage, path);\n      await deleteObject(storageRef);\n    } catch (error) {\n      console.error('Error deleting file:', error);\n      throw new Error('Failed to delete file');\n    }\n  }\n\n  // Get file name from URL\n  static getFileNameFromUrl(url: string): string {\n    try {\n      const urlParts = url.split('/');\n      const pathIndex = urlParts.findIndex(part => part === 'o') + 1;\n      if (pathIndex === 0) {\n        return 'Unknown file';\n      }\n\n      const encodedPath = urlParts[pathIndex].split('?')[0];\n      const path = decodeURIComponent(encodedPath);\n      const fileName = path.split('/').pop() || 'Unknown file';\n\n      // Remove timestamp prefix if present\n      const parts = fileName.split('_');\n      if (parts.length > 1 && !isNaN(Number(parts[parts.length - 1].split('.')[0]))) {\n        return parts.slice(0, -1).join('_') + '.' + fileName.split('.').pop();\n      }\n\n      return fileName;\n    } catch (error) {\n      console.error('Error parsing file name from URL:', error);\n      return 'Unknown file';\n    }\n  }\n\n  // Validate file type and size\n  static validateFile(\n    file: File,\n    allowedTypes: string[] = ['.pdf', '.doc', '.docx', '.txt', '.jpg', '.jpeg', '.png'],\n    maxSizeMB: number = 10\n  ): { isValid: boolean; error?: string } {\n    // Check file size\n    if (file.size > maxSizeMB * 1024 * 1024) {\n      return {\n        isValid: false,\n        error: `File size must be less than ${maxSizeMB}MB`\n      };\n    }\n\n    // Check file type\n    const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();\n    const isValidType = allowedTypes.includes(fileExtension);\n\n    if (!isValidType) {\n      return {\n        isValid: false,\n        error: `Please select a valid file type: ${allowedTypes.join(', ')}`\n      };\n    }\n\n    return { isValid: true };\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;;;AAEO,MAAM;IACX,oCAAoC;IACpC,aAAa,WACX,IAAU,EACV,IAAY,EACZ,UAAuC,EACtB;QACjB,IAAI;YACF,0CAA0C;YAC1C,MAAM,aAAa,CAAA,GAAA,oLAAA,CAAA,MAAG,AAAD,EAAE,yHAAA,CAAA,UAAO,EAAE;YAEhC,kBAAkB;YAClB,MAAM,WAAW,MAAM,CAAA,GAAA,oLAAA,CAAA,cAAW,AAAD,EAAE,YAAY;YAE/C,uBAAuB;YACvB,MAAM,cAAc,MAAM,CAAA,GAAA,oLAAA,CAAA,iBAAc,AAAD,EAAE,SAAS,GAAG;YAErD,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,2BAA2B;IAC3B,aAAa,uBACX,IAAU,EACV,UAAkB,EAClB,YAA6E,EAC5D;QACjB,MAAM,YAAY,KAAK,GAAG;QAC1B,MAAM,gBAAgB,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG;QAC9C,MAAM,WAAW,GAAG,aAAa,CAAC,EAAE,UAAU,CAAC,EAAE,eAAe;QAChE,MAAM,OAAO,CAAC,UAAU,EAAE,WAAW,WAAW,EAAE,UAAU;QAE5D,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM;IAC/B;IAEA,sCAAsC;IACtC,aAAa,WAAW,GAAW,EAAiB;QAClD,IAAI;YACF,gCAAgC;YAChC,MAAM,WAAW,IAAI,KAAK,CAAC;YAC3B,MAAM,YAAY,SAAS,SAAS,CAAC,CAAA,OAAQ,SAAS,OAAO;YAC7D,IAAI,cAAc,GAAG;gBACnB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,cAAc,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;YACrD,MAAM,OAAO,mBAAmB;YAEhC,MAAM,aAAa,CAAA,GAAA,oLAAA,CAAA,MAAG,AAAD,EAAE,yHAAA,CAAA,UAAO,EAAE;YAChC,MAAM,CAAA,GAAA,oLAAA,CAAA,eAAY,AAAD,EAAE;QACrB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,yBAAyB;IACzB,OAAO,mBAAmB,GAAW,EAAU;QAC7C,IAAI;YACF,MAAM,WAAW,IAAI,KAAK,CAAC;YAC3B,MAAM,YAAY,SAAS,SAAS,CAAC,CAAA,OAAQ,SAAS,OAAO;YAC7D,IAAI,cAAc,GAAG;gBACnB,OAAO;YACT;YAEA,MAAM,cAAc,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;YACrD,MAAM,OAAO,mBAAmB;YAChC,MAAM,WAAW,KAAK,KAAK,CAAC,KAAK,GAAG,MAAM;YAE1C,qCAAqC;YACrC,MAAM,QAAQ,SAAS,KAAK,CAAC;YAC7B,IAAI,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,OAAO,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI;gBAC7E,OAAO,MAAM,KAAK,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,MAAM,SAAS,KAAK,CAAC,KAAK,GAAG;YACrE;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;YACnD,OAAO;QACT;IACF;IAEA,8BAA8B;IAC9B,OAAO,aACL,IAAU,EACV,eAAyB;QAAC;QAAQ;QAAQ;QAAS;QAAQ;QAAQ;QAAS;KAAO,EACnF,YAAoB,EAAE,EACgB;QACtC,kBAAkB;QAClB,IAAI,KAAK,IAAI,GAAG,YAAY,OAAO,MAAM;YACvC,OAAO;gBACL,SAAS;gBACT,OAAO,CAAC,4BAA4B,EAAE,UAAU,EAAE,CAAC;YACrD;QACF;QAEA,kBAAkB;QAClB,MAAM,gBAAgB,MAAM,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI;QACxD,MAAM,cAAc,aAAa,QAAQ,CAAC;QAE1C,IAAI,CAAC,aAAa;YAChB,OAAO;gBACL,SAAS;gBACT,OAAO,CAAC,iCAAiC,EAAE,aAAa,IAAI,CAAC,OAAO;YACtE;QACF;QAEA,OAAO;YAAE,SAAS;QAAK;IACzB;AACF", "debugId": null}}, {"offset": {"line": 1518, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/source/repos/ravihani/valtics/valtics-ai/app/admin/templates/%5Bid%5D/edit/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { useRouter, useParams } from 'next/navigation';\nimport Link from 'next/link';\nimport { doc, getDoc, updateDoc, Timestamp, addDoc, collection } from 'firebase/firestore';\nimport { db } from '@/lib/firebase/config';\nimport { Template, Brand } from '@/types';\nimport Navigation from '@/components/Navigation';\nimport RichTextEditor from '@/components/RichTextEditor';\nimport SimpleRichTextEditor from '@/components/SimpleRichTextEditor';\nimport FileUpload from '@/components/FileUpload';\nimport { StorageService } from '@/lib/firebase/storage';\n\nexport default function EditTemplate() {\n  const { user, loading, isAdmin } = useAuth();\n  const router = useRouter();\n  const params = useParams();\n  const templateId = params.id as string;\n\n  const [template, setTemplate] = useState<Template | null>(null);\n  const [brand, setBrand] = useState<Brand | null>(null);\n  const [loadingData, setLoadingData] = useState(true);\n  const [submitting, setSubmitting] = useState(false);\n  const [currentStep, setCurrentStep] = useState<'step1' | 'step2'>('step2');\n  const [useSimpleEditor, setUseSimpleEditor] = useState(true); // Default to simple editor for React 19 compatibility\n\n  const [formData, setFormData] = useState({\n    enterpriseNeed: '',\n    solutionDescriptionDocument: '',\n    riskOfNoInvestment: '',\n    enterpriseNeedFileUrl: '',\n    solutionDescriptionFileUrl: '',\n    riskOfNoInvestmentFileUrl: ''\n  });\n\n  const [uploadingFiles, setUploadingFiles] = useState({\n    enterpriseNeed: false,\n    solutionDescription: false,\n    riskOfNoInvestment: false\n  });\n\n  const [step1Data, setStep1Data] = useState({\n    solutionProviderName: '',\n    solutionName: '',\n    solutionDescription: '',\n    templateVersion: '',\n    templateVersionDate: '',\n    category: '',\n    price: 0\n  });\n\n  useEffect(() => {\n    if (!loading && !isAdmin) {\n      router.push('/');\n      return;\n    }\n    if (isAdmin && templateId) {\n      fetchTemplateData();\n    }\n  }, [loading, isAdmin, router, templateId]);\n\n  const fetchTemplateData = async () => {\n    try {\n      setLoadingData(true);\n\n      // Fetch template\n      const templateDoc = await getDoc(doc(db, 'templates', templateId));\n      if (templateDoc.exists()) {\n        const templateData = { id: templateDoc.id, ...templateDoc.data() } as Template;\n        setTemplate(templateData);\n\n        // Set form data\n        setFormData({\n          enterpriseNeed: templateData.enterpriseNeed || '',\n          solutionDescriptionDocument: templateData.solutionDescriptionDocument || '',\n          riskOfNoInvestment: templateData.riskOfNoInvestment || '',\n          enterpriseNeedFileUrl: templateData.enterpriseNeedFileUrl || '',\n          solutionDescriptionFileUrl: templateData.solutionDescriptionFileUrl || '',\n          riskOfNoInvestmentFileUrl: templateData.riskOfNoInvestmentFileUrl || ''\n        });\n\n        setStep1Data({\n          solutionProviderName: templateData.solutionProviderName || '',\n          solutionName: templateData.solutionName || '',\n          solutionDescription: templateData.solutionDescription || '',\n          templateVersion: templateData.templateVersion || '',\n          templateVersionDate: templateData.templateVersionDate ?\n            (templateData.templateVersionDate instanceof Date\n              ? templateData.templateVersionDate.toISOString().split('T')[0]\n              : new Date((templateData.templateVersionDate as any).seconds * 1000).toISOString().split('T')[0]\n            ) : '',\n          category: templateData.category || '',\n          price: templateData.price || 0\n        });\n\n        // Fetch brand\n        const brandDoc = await getDoc(doc(db, 'brands', templateData.brandId));\n        if (brandDoc.exists()) {\n          setBrand({ id: brandDoc.id, ...brandDoc.data() } as Brand);\n        }\n      } else {\n        router.push('/admin');\n      }\n    } catch (error) {\n      console.error('Error fetching template data:', error);\n      router.push('/admin');\n    } finally {\n      setLoadingData(false);\n    }\n  };\n\n  const handleStep1Update = async (e: React.FormEvent) => {\n    e.preventDefault();\n\n    if (!template) return;\n\n    try {\n      setSubmitting(true);\n\n      await updateDoc(doc(db, 'templates', templateId), {\n        solutionProviderName: step1Data.solutionProviderName,\n        solutionName: step1Data.solutionName,\n        solutionDescription: step1Data.solutionDescription,\n        templateVersion: step1Data.templateVersion,\n        templateVersionDate: Timestamp.fromDate(new Date(step1Data.templateVersionDate)),\n        category: step1Data.category,\n        price: step1Data.price,\n        name: step1Data.solutionName, // Update template name\n        description: step1Data.solutionDescription, // Update template description\n        updatedAt: Timestamp.now()\n      });\n\n      alert('Step 1 information updated successfully!');\n      await fetchTemplateData(); // Refresh data\n\n    } catch (error) {\n      console.error('Error updating template:', error);\n      alert('Error updating template. Please try again.');\n    } finally {\n      setSubmitting(false);\n    }\n  };\n\n  const handleFileUpload = async (\n    file: File,\n    documentType: 'enterpriseNeed' | 'solutionDescription' | 'riskOfNoInvestment'\n  ) => {\n    if (!template) return;\n\n    try {\n      setUploadingFiles(prev => ({ ...prev, [documentType]: true }));\n\n      const fileUrl = await StorageService.uploadTemplateDocument(file, templateId, documentType);\n\n      // Update form data\n      const fieldName = `${documentType}FileUrl` as keyof typeof formData;\n      setFormData(prev => ({ ...prev, [fieldName]: fileUrl }));\n\n      // Update database immediately\n      await updateDoc(doc(db, 'templates', templateId), {\n        [`${documentType}FileUrl`]: fileUrl,\n        updatedAt: Timestamp.now()\n      });\n\n      alert('File uploaded successfully!');\n\n    } catch (error) {\n      console.error('Error uploading file:', error);\n      alert('Error uploading file. Please try again.');\n    } finally {\n      setUploadingFiles(prev => ({ ...prev, [documentType]: false }));\n    }\n  };\n\n  const handleFileRemove = async (documentType: 'enterpriseNeed' | 'solutionDescription' | 'riskOfNoInvestment') => {\n    if (!template) return;\n\n    try {\n      const fieldName = `${documentType}FileUrl` as keyof typeof formData;\n      const currentUrl = formData[fieldName];\n\n      if (currentUrl) {\n        // Delete from storage\n        await StorageService.deleteFile(currentUrl);\n      }\n\n      // Update form data\n      setFormData(prev => ({ ...prev, [fieldName]: '' }));\n\n      // Update database\n      await updateDoc(doc(db, 'templates', templateId), {\n        [`${documentType}FileUrl`]: '',\n        updatedAt: Timestamp.now()\n      });\n\n      alert('File removed successfully!');\n\n    } catch (error) {\n      console.error('Error removing file:', error);\n      alert('Error removing file. Please try again.');\n    }\n  };\n\n  const handleStep2Submit = async (e: React.FormEvent) => {\n    e.preventDefault();\n\n    if (!template) return;\n\n    try {\n      setSubmitting(true);\n\n      await updateDoc(doc(db, 'templates', templateId), {\n        enterpriseNeed: formData.enterpriseNeed,\n        solutionDescriptionDocument: formData.solutionDescriptionDocument,\n        riskOfNoInvestment: formData.riskOfNoInvestment,\n        enterpriseNeedFileUrl: formData.enterpriseNeedFileUrl,\n        solutionDescriptionFileUrl: formData.solutionDescriptionFileUrl,\n        riskOfNoInvestmentFileUrl: formData.riskOfNoInvestmentFileUrl,\n        step2Completed: true,\n        updatedAt: Timestamp.now()\n      });\n\n      alert('Documents saved successfully! You can now publish the template.');\n      await fetchTemplateData(); // Refresh data\n\n    } catch (error) {\n      console.error('Error updating template:', error);\n      alert('Error updating template. Please try again.');\n    } finally {\n      setSubmitting(false);\n    }\n  };\n\n  const handlePublishTemplate = async () => {\n    if (!template) return;\n\n    if (!template.step1Completed || !template.step2Completed) {\n      alert('Please complete both steps before publishing.');\n      return;\n    }\n\n    if (!formData.enterpriseNeed || !formData.solutionDescriptionDocument || !formData.riskOfNoInvestment) {\n      alert('Please fill in all three documents before publishing.');\n      return;\n    }\n\n    const confirmPublish = window.confirm(\n      'Are you sure you want to publish this template? Once published, it cannot be edited. You can only create new versions from published templates.'\n    );\n\n    if (!confirmPublish) return;\n\n    try {\n      setSubmitting(true);\n\n      await updateDoc(doc(db, 'templates', templateId), {\n        status: 'published',\n        isActive: true,\n        publishedAt: Timestamp.now(),\n        updatedAt: Timestamp.now()\n      });\n\n      alert('Template published successfully! It can no longer be edited.');\n      await fetchTemplateData(); // Refresh to show published state\n\n    } catch (error) {\n      console.error('Error publishing template:', error);\n      alert('Error publishing template. Please try again.');\n    } finally {\n      setSubmitting(false);\n    }\n  };\n\n  const handleCreateNewVersion = async () => {\n    if (!template || !brand) return;\n\n    const confirmCreate = window.confirm(\n      'This will create a new draft version of this template that you can edit. Continue?'\n    );\n\n    if (!confirmCreate) return;\n\n    try {\n      setSubmitting(true);\n\n      // Create new template with incremented version\n      const currentVersion = template.templateVersion || '1.0';\n      const versionParts = currentVersion.split('.');\n      const majorVersion = parseInt(versionParts[0]) || 1;\n      const minorVersion = parseInt(versionParts[1]) || 0;\n      const newVersion = `${majorVersion}.${minorVersion + 1}`;\n\n      const newTemplateData = {\n        name: template.name,\n        description: template.description,\n        brandId: template.brandId,\n        category: template.category,\n        price: template.price,\n        solutionProviderName: template.solutionProviderName,\n        solutionName: template.solutionName,\n        solutionDescription: template.solutionDescription,\n        templateVersion: newVersion,\n        templateVersionDate: Timestamp.now(),\n        status: 'draft' as const,\n        step1Completed: true,\n        step2Completed: false,\n        isActive: false,\n        fileUrls: {},\n        // Copy document content but not file URLs (they can upload new files)\n        enterpriseNeed: template.enterpriseNeed || '',\n        solutionDescriptionDocument: template.solutionDescriptionDocument || '',\n        riskOfNoInvestment: template.riskOfNoInvestment || '',\n        // Don't copy file URLs - let them upload new files\n        enterpriseNeedFileUrl: '',\n        solutionDescriptionFileUrl: '',\n        riskOfNoInvestmentFileUrl: '',\n        createdAt: Timestamp.now(),\n        updatedAt: Timestamp.now(),\n        // Reference to original template\n        basedOnTemplateId: templateId,\n        basedOnVersion: template.templateVersion\n      };\n\n      const docRef = await addDoc(collection(db, 'templates'), newTemplateData);\n\n      alert(`New version ${newVersion} created successfully!`);\n      router.push(`/admin/templates/${docRef.id}/edit`);\n\n    } catch (error) {\n      console.error('Error creating new version:', error);\n      alert('Error creating new version. Please try again.');\n    } finally {\n      setSubmitting(false);\n    }\n  };\n\n  const handleSaveDraft = async () => {\n    if (!template) return;\n\n    try {\n      setSubmitting(true);\n\n      await updateDoc(doc(db, 'templates', templateId), {\n        enterpriseNeed: formData.enterpriseNeed,\n        solutionDescriptionDocument: formData.solutionDescriptionDocument,\n        riskOfNoInvestment: formData.riskOfNoInvestment,\n        enterpriseNeedFileUrl: formData.enterpriseNeedFileUrl,\n        solutionDescriptionFileUrl: formData.solutionDescriptionFileUrl,\n        riskOfNoInvestmentFileUrl: formData.riskOfNoInvestmentFileUrl,\n        status: 'draft',\n        updatedAt: Timestamp.now()\n      });\n\n      alert('Template saved as draft successfully!');\n\n    } catch (error) {\n      console.error('Error saving template:', error);\n      alert('Error saving template. Please try again.');\n    } finally {\n      setSubmitting(false);\n    }\n  };\n\n  if (loading || loadingData) {\n    return (\n      <div className=\"flex min-h-screen items-center justify-center\">\n        <div className=\"text-xl\">Loading...</div>\n      </div>\n    );\n  }\n\n  if (!isAdmin || !template) {\n    return null;\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900\">\n      <Navigation />\n\n      <div className=\"max-w-6xl mx-auto py-6 sm:px-6 lg:px-8\">\n        <div className=\"px-4 py-6 sm:px-0\">\n          {/* Header */}\n          <div className=\"mb-8\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white\">\n                  {template.status === 'published' ? 'View Template' : 'Edit Template'}\n                </h1>\n                <p className=\"mt-2 text-gray-600 dark:text-gray-300\">\n                  {template.solutionName} - {brand?.name}\n                </p>\n                <div className=\"mt-2 flex items-center space-x-4\">\n                  <span className={`px-2 py-1 text-xs font-medium rounded-full ${\n                    template.status === 'published'\n                      ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'\n                      : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'\n                  }`}>\n                    {template.status === 'published' ? 'Published' : 'Draft'}\n                  </span>\n                  <span className=\"text-sm text-gray-500 dark:text-gray-400\">\n                    Version {template.templateVersion}\n                  </span>\n                  {template.basedOnTemplateId && (\n                    <span className=\"text-xs text-blue-600 dark:text-blue-400\">\n                      Based on v{template.basedOnVersion}\n                    </span>\n                  )}\n                </div>\n                {template.status === 'published' && (\n                  <div className=\"mt-2 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md\">\n                    <p className=\"text-sm text-blue-800 dark:text-blue-200\">\n                      <strong>Note:</strong> This template is published and cannot be edited. You can create a new version to make changes.\n                    </p>\n                  </div>\n                )}\n              </div>\n              <div className=\"flex space-x-3\">\n                {template.status === 'published' && (\n                  <button\n                    onClick={handleCreateNewVersion}\n                    disabled={submitting}\n                    className=\"bg-blue-600 dark:bg-blue-700 text-white px-4 py-2 rounded-md font-medium hover:bg-blue-700 dark:hover:bg-blue-800 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2\"\n                  >\n                    {submitting && (\n                      <svg className=\"animate-spin -ml-1 mr-2 h-4 w-4 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n                        <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                        <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                      </svg>\n                    )}\n                    <span>{submitting ? 'Creating...' : 'Create New Version'}</span>\n                  </button>\n                )}\n                <Link\n                  href=\"/admin\"\n                  className=\"bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 px-4 py-2 rounded-md font-medium hover:bg-gray-300 dark:hover:bg-gray-600\"\n                >\n                  Back to Admin\n                </Link>\n              </div>\n            </div>\n          </div>\n\n          {/* Progress Indicator */}\n          <div className=\"mb-8\">\n            <div className=\"flex items-center\">\n              <div className={`flex items-center ${template.step1Completed ? 'text-green-600 dark:text-green-400' : 'text-blue-600 dark:text-blue-400'}`}>\n                <div className={`flex items-center justify-center w-8 h-8 ${template.step1Completed ? 'bg-green-600 dark:bg-green-500' : 'bg-blue-600 dark:bg-blue-500'} text-white rounded-full text-sm font-medium`}>\n                  {template.step1Completed ? '✓' : '1'}\n                </div>\n                <span className=\"ml-2 text-sm font-medium\">Basic Information</span>\n              </div>\n              <div className=\"flex-1 mx-4 h-0.5 bg-gray-200 dark:bg-gray-700\"></div>\n              <div className={`flex items-center ${template.step2Completed ? 'text-green-600 dark:text-green-400' : 'text-blue-600 dark:text-blue-400'}`}>\n                <div className={`flex items-center justify-center w-8 h-8 ${template.step2Completed ? 'bg-green-600 dark:bg-green-500' : 'bg-blue-600 dark:bg-blue-500'} text-white rounded-full text-sm font-medium`}>\n                  {template.step2Completed ? '✓' : '2'}\n                </div>\n                <span className=\"ml-2 text-sm font-medium\">Documents</span>\n              </div>\n            </div>\n          </div>\n\n          {/* Step Navigation */}\n          <div className=\"mb-6\">\n            <div className=\"flex space-x-1 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg\">\n              <button\n                onClick={() => setCurrentStep('step1')}\n                disabled={template.status === 'published'}\n                className={`flex-1 py-2 px-4 text-sm font-medium rounded-md transition-colors ${\n                  currentStep === 'step1'\n                    ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow'\n                    : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200'\n                } ${template.status === 'published' ? 'cursor-not-allowed opacity-60' : ''}`}\n              >\n                {template.status === 'published' ? 'View Basic Information' : 'Edit Basic Information'}\n              </button>\n              <button\n                onClick={() => setCurrentStep('step2')}\n                disabled={template.status === 'published'}\n                className={`flex-1 py-2 px-4 text-sm font-medium rounded-md transition-colors ${\n                  currentStep === 'step2'\n                    ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow'\n                    : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200'\n                } ${template.status === 'published' ? 'cursor-not-allowed opacity-60' : ''}`}\n              >\n                {template.status === 'published' ? 'View Documents' : 'Edit Documents'}\n              </button>\n            </div>\n          </div>\n\n          {/* Step 1 Content */}\n          {currentStep === 'step1' && (\n            <div className=\"bg-white dark:bg-gray-800 shadow rounded-lg\">\n              <form onSubmit={handleStep1Update} className=\"p-6 space-y-6\">\n                <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">\n                  {template.status === 'published' ? 'Basic Information (Read Only)' : 'Basic Information'}\n                </h3>\n\n                {/* Solution Provider Name */}\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    Solution Provider Name *\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={step1Data.solutionProviderName}\n                    onChange={(e) => setStep1Data(prev => ({ ...prev, solutionProviderName: e.target.value }))}\n                    disabled={template.status === 'published'}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500 disabled:opacity-60 disabled:cursor-not-allowed\"\n                    required\n                  />\n                </div>\n\n                {/* Solution Name */}\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    Solution Name *\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={step1Data.solutionName}\n                    onChange={(e) => setStep1Data(prev => ({ ...prev, solutionName: e.target.value }))}\n                    disabled={template.status === 'published'}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500 disabled:opacity-60 disabled:cursor-not-allowed\"\n                    required\n                  />\n                </div>\n\n                {/* Solution Description */}\n                <div>\n                  <div className=\"flex justify-between items-center mb-2\">\n                    <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\n                      Solution Description *\n                    </label>\n                    <button\n                      type=\"button\"\n                      onClick={() => setUseSimpleEditor(!useSimpleEditor)}\n                      className=\"text-xs text-blue-600 dark:text-blue-400 hover:underline\"\n                    >\n                      {useSimpleEditor ? 'Try Advanced Editor' : 'Use Simple Editor'}\n                    </button>\n                  </div>\n                  {useSimpleEditor ? (\n                    <SimpleRichTextEditor\n                      value={step1Data.solutionDescription}\n                      onChange={(value) => setStep1Data(prev => ({ ...prev, solutionDescription: value }))}\n                      placeholder=\"Enter detailed solution description...\"\n                      className=\"border border-gray-300 dark:border-gray-600 rounded-md\"\n                      disabled={template.status === 'published'}\n                    />\n                  ) : (\n                    <RichTextEditor\n                      value={step1Data.solutionDescription}\n                      onChange={(value) => setStep1Data(prev => ({ ...prev, solutionDescription: value }))}\n                      placeholder=\"Enter detailed solution description...\"\n                      className=\"border border-gray-300 dark:border-gray-600 rounded-md\"\n                      disabled={template.status === 'published'}\n                    />\n                  )}\n                </div>\n\n                {/* Template Version and Date */}\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                      Template Version *\n                    </label>\n                    <input\n                      type=\"text\"\n                      value={step1Data.templateVersion}\n                      onChange={(e) => setStep1Data(prev => ({ ...prev, templateVersion: e.target.value }))}\n                      disabled={template.status === 'published'}\n                      className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500 disabled:opacity-60 disabled:cursor-not-allowed\"\n                      required\n                    />\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                      Template Version Date *\n                    </label>\n                    <input\n                      type=\"date\"\n                      value={step1Data.templateVersionDate}\n                      onChange={(e) => setStep1Data(prev => ({ ...prev, templateVersionDate: e.target.value }))}\n                      disabled={template.status === 'published'}\n                      className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500 disabled:opacity-60 disabled:cursor-not-allowed\"\n                      required\n                    />\n                  </div>\n                </div>\n\n                {/* Category and Price */}\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                      Category\n                    </label>\n                    <select\n                      value={step1Data.category}\n                      onChange={(e) => setStep1Data(prev => ({ ...prev, category: e.target.value }))}\n                      disabled={template.status === 'published'}\n                      className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500 disabled:opacity-60 disabled:cursor-not-allowed\"\n                    >\n                      <option value=\"Business Analysis\">Business Analysis</option>\n                      <option value=\"Financial Planning\">Financial Planning</option>\n                      <option value=\"Technology Assessment\">Technology Assessment</option>\n                      <option value=\"Risk Management\">Risk Management</option>\n                      <option value=\"Strategic Planning\">Strategic Planning</option>\n                    </select>\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                      Price ($)\n                    </label>\n                    <input\n                      type=\"number\"\n                      min=\"0\"\n                      step=\"0.01\"\n                      value={step1Data.price}\n                      onChange={(e) => setStep1Data(prev => ({ ...prev, price: parseFloat(e.target.value) || 0 }))}\n                      disabled={template.status === 'published'}\n                      className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500 disabled:opacity-60 disabled:cursor-not-allowed\"\n                    />\n                  </div>\n                </div>\n\n                {/* Submit Button */}\n                {template.status !== 'published' && (\n                  <div className=\"flex justify-end pt-6 border-t border-gray-200 dark:border-gray-700\">\n                    <button\n                      type=\"submit\"\n                      disabled={submitting}\n                      className=\"bg-blue-600 dark:bg-blue-700 text-white px-6 py-2 rounded-md font-medium hover:bg-blue-700 dark:hover:bg-blue-800 disabled:opacity-50 disabled:cursor-not-allowed\"\n                    >\n                      {submitting ? 'Updating...' : 'Update Basic Information'}\n                    </button>\n                  </div>\n                )}\n              </form>\n            </div>\n          )}\n\n          {/* Step 2 Content */}\n          {currentStep === 'step2' && (\n            <div className=\"bg-white dark:bg-gray-800 shadow rounded-lg\">\n              <form onSubmit={handleStep2Submit} className=\"p-6 space-y-8\">\n                <div>\n                  <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">\n                    {template.status === 'published' ? 'View Documents (Read Only)' : 'Generate Documents'}\n                  </h3>\n                  <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-6\">\n                    {template.status === 'published'\n                      ? 'View the three essential documents for this Business Value Analysis template.'\n                      : 'Create the three essential documents for your Business Value Analysis template.'\n                    }\n                  </p>\n                </div>\n\n                {/* Enterprise Need */}\n                <div>\n                  <div className=\"flex justify-between items-center mb-2\">\n                    <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\n                      Enterprise Need *\n                    </label>\n                    <button\n                      type=\"button\"\n                      onClick={() => setUseSimpleEditor(!useSimpleEditor)}\n                      className=\"text-xs text-blue-600 dark:text-blue-400 hover:underline\"\n                    >\n                      {useSimpleEditor ? 'Try Advanced Editor' : 'Use Simple Editor'}\n                    </button>\n                  </div>\n                  <p className=\"text-sm text-gray-500 dark:text-gray-400 mb-3\">\n                    Describe the business need or challenge that this solution addresses.\n                  </p>\n\n                  {/* Text Editor */}\n                  <div className=\"mb-4\">\n                    {useSimpleEditor ? (\n                      <SimpleRichTextEditor\n                        value={formData.enterpriseNeed}\n                        onChange={(value) => setFormData(prev => ({ ...prev, enterpriseNeed: value }))}\n                        placeholder=\"Describe the enterprise need that this solution addresses...\"\n                        className=\"border border-gray-300 dark:border-gray-600 rounded-md\"\n                        disabled={template.status === 'published'}\n                      />\n                    ) : (\n                      <RichTextEditor\n                        value={formData.enterpriseNeed}\n                        onChange={(value) => setFormData(prev => ({ ...prev, enterpriseNeed: value }))}\n                        placeholder=\"Describe the enterprise need that this solution addresses...\"\n                        className=\"border border-gray-300 dark:border-gray-600 rounded-md\"\n                        disabled={template.status === 'published'}\n                      />\n                    )}\n                  </div>\n\n                  {/* File Upload */}\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                      Upload Supporting Document or Image (Optional)\n                    </label>\n                    <FileUpload\n                      onFileSelect={(file) => handleFileUpload(file, 'enterpriseNeed')}\n                      onFileRemove={() => handleFileRemove('enterpriseNeed')}\n                      currentFileUrl={formData.enterpriseNeedFileUrl}\n                      currentFileName={formData.enterpriseNeedFileUrl ? StorageService.getFileNameFromUrl(formData.enterpriseNeedFileUrl) : undefined}\n                      disabled={uploadingFiles.enterpriseNeed || template.status === 'published'}\n                      className=\"mt-2\"\n                    />\n                  </div>\n                </div>\n\n                {/* Solution Description Document */}\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    Solution Description *\n                  </label>\n                  <p className=\"text-sm text-gray-500 dark:text-gray-400 mb-3\">\n                    Provide a comprehensive description of how your solution works and its key features.\n                  </p>\n\n                  {/* Text Editor */}\n                  <div className=\"mb-4\">\n                    {useSimpleEditor ? (\n                      <SimpleRichTextEditor\n                        value={formData.solutionDescriptionDocument}\n                        onChange={(value) => setFormData(prev => ({ ...prev, solutionDescriptionDocument: value }))}\n                        placeholder=\"Provide a detailed description of your solution...\"\n                        className=\"border border-gray-300 dark:border-gray-600 rounded-md\"\n                        disabled={template.status === 'published'}\n                      />\n                    ) : (\n                      <RichTextEditor\n                        value={formData.solutionDescriptionDocument}\n                        onChange={(value) => setFormData(prev => ({ ...prev, solutionDescriptionDocument: value }))}\n                        placeholder=\"Provide a detailed description of your solution...\"\n                        className=\"border border-gray-300 dark:border-gray-600 rounded-md\"\n                        disabled={template.status === 'published'}\n                      />\n                    )}\n                  </div>\n\n                  {/* File Upload */}\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                      Upload Supporting Document or Image (Optional)\n                    </label>\n                    <FileUpload\n                      onFileSelect={(file) => handleFileUpload(file, 'solutionDescription')}\n                      onFileRemove={() => handleFileRemove('solutionDescription')}\n                      currentFileUrl={formData.solutionDescriptionFileUrl}\n                      currentFileName={formData.solutionDescriptionFileUrl ? StorageService.getFileNameFromUrl(formData.solutionDescriptionFileUrl) : undefined}\n                      disabled={uploadingFiles.solutionDescription || template.status === 'published'}\n                      className=\"mt-2\"\n                    />\n                  </div>\n                </div>\n\n                {/* Risk of No Investment */}\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    Risk of No Investment *\n                  </label>\n                  <p className=\"text-sm text-gray-500 dark:text-gray-400 mb-3\">\n                    Explain the potential risks and consequences of not implementing this solution.\n                  </p>\n\n                  {/* Text Editor */}\n                  <div className=\"mb-4\">\n                    {useSimpleEditor ? (\n                      <SimpleRichTextEditor\n                        value={formData.riskOfNoInvestment}\n                        onChange={(value) => setFormData(prev => ({ ...prev, riskOfNoInvestment: value }))}\n                        placeholder=\"Describe the risks of not investing in this solution...\"\n                        className=\"border border-gray-300 dark:border-gray-600 rounded-md\"\n                        disabled={template.status === 'published'}\n                      />\n                    ) : (\n                      <RichTextEditor\n                        value={formData.riskOfNoInvestment}\n                        onChange={(value) => setFormData(prev => ({ ...prev, riskOfNoInvestment: value }))}\n                        placeholder=\"Describe the risks of not investing in this solution...\"\n                        className=\"border border-gray-300 dark:border-gray-600 rounded-md\"\n                        disabled={template.status === 'published'}\n                      />\n                    )}\n                  </div>\n\n                  {/* File Upload */}\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                      Upload Supporting Document or Image (Optional)\n                    </label>\n                    <FileUpload\n                      onFileSelect={(file) => handleFileUpload(file, 'riskOfNoInvestment')}\n                      onFileRemove={() => handleFileRemove('riskOfNoInvestment')}\n                      currentFileUrl={formData.riskOfNoInvestmentFileUrl}\n                      currentFileName={formData.riskOfNoInvestmentFileUrl ? StorageService.getFileNameFromUrl(formData.riskOfNoInvestmentFileUrl) : undefined}\n                      disabled={uploadingFiles.riskOfNoInvestment || template.status === 'published'}\n                      className=\"mt-2\"\n                    />\n                  </div>\n                </div>\n\n                {/* Action Buttons */}\n                {template.status !== 'published' && (\n                  <div className=\"flex justify-between pt-6 border-t border-gray-200 dark:border-gray-700\">\n                  <button\n                    type=\"button\"\n                    onClick={handleSaveDraft}\n                    disabled={submitting}\n                    className=\"bg-gray-600 dark:bg-gray-700 text-white px-6 py-2 rounded-md font-medium hover:bg-gray-700 dark:hover:bg-gray-800 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2\"\n                  >\n                    {submitting && (\n                      <svg className=\"animate-spin -ml-1 mr-2 h-4 w-4 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n                        <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                        <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                      </svg>\n                    )}\n                    <span>{submitting ? 'Saving...' : 'Save as Draft'}</span>\n                  </button>\n\n                  <div className=\"flex space-x-3\">\n                    <button\n                      type=\"submit\"\n                      disabled={submitting}\n                      className=\"bg-blue-600 dark:bg-blue-700 text-white px-6 py-2 rounded-md font-medium hover:bg-blue-700 dark:hover:bg-blue-800 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2\"\n                    >\n                      {submitting && (\n                        <svg className=\"animate-spin -ml-1 mr-2 h-4 w-4 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n                          <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                          <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                        </svg>\n                      )}\n                      <span>{submitting ? 'Saving...' : 'Save Documents'}</span>\n                    </button>\n\n                    {template.step2Completed && (\n                      <button\n                        type=\"button\"\n                        onClick={handlePublishTemplate}\n                        disabled={submitting || template.status === 'published'}\n                        className=\"bg-green-600 dark:bg-green-700 text-white px-6 py-2 rounded-md font-medium hover:bg-green-700 dark:hover:bg-green-800 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2\"\n                      >\n                        {submitting && template.status !== 'published' && (\n                          <svg className=\"animate-spin -ml-1 mr-2 h-4 w-4 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n                            <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                            <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                          </svg>\n                        )}\n                        <span>{template.status === 'published' ? 'Published' : submitting ? 'Publishing...' : 'Publish Template'}</span>\n                      </button>\n                    )}\n                  </div>\n                </div>\n                )}\n              </form>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AACA;AAEA;AACA;AACA;AACA;AACA;AAbA;;;;;;;;;;;;;AAee,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,UAAO,AAAD;IACzC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,aAAa,OAAO,EAAE;IAE5B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IAC1D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB;IAClE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,sDAAsD;IAEpH,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,gBAAgB;QAChB,6BAA6B;QAC7B,oBAAoB;QACpB,uBAAuB;QACvB,4BAA4B;QAC5B,2BAA2B;IAC7B;IAEA,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACnD,gBAAgB;QAChB,qBAAqB;QACrB,oBAAoB;IACtB;IAEA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACzC,sBAAsB;QACtB,cAAc;QACd,qBAAqB;QACrB,iBAAiB;QACjB,qBAAqB;QACrB,UAAU;QACV,OAAO;IACT;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW,CAAC,SAAS;YACxB,OAAO,IAAI,CAAC;YACZ;QACF;QACA,IAAI,WAAW,YAAY;YACzB;QACF;IACF,GAAG;QAAC;QAAS;QAAS;QAAQ;KAAW;IAEzC,MAAM,oBAAoB;QACxB,IAAI;YACF,eAAe;YAEf,iBAAiB;YACjB,MAAM,cAAc,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,aAAa;YACtD,IAAI,YAAY,MAAM,IAAI;gBACxB,MAAM,eAAe;oBAAE,IAAI,YAAY,EAAE;oBAAE,GAAG,YAAY,IAAI,EAAE;gBAAC;gBACjE,YAAY;gBAEZ,gBAAgB;gBAChB,YAAY;oBACV,gBAAgB,aAAa,cAAc,IAAI;oBAC/C,6BAA6B,aAAa,2BAA2B,IAAI;oBACzE,oBAAoB,aAAa,kBAAkB,IAAI;oBACvD,uBAAuB,aAAa,qBAAqB,IAAI;oBAC7D,4BAA4B,aAAa,0BAA0B,IAAI;oBACvE,2BAA2B,aAAa,yBAAyB,IAAI;gBACvE;gBAEA,aAAa;oBACX,sBAAsB,aAAa,oBAAoB,IAAI;oBAC3D,cAAc,aAAa,YAAY,IAAI;oBAC3C,qBAAqB,aAAa,mBAAmB,IAAI;oBACzD,iBAAiB,aAAa,eAAe,IAAI;oBACjD,qBAAqB,aAAa,mBAAmB,GAClD,aAAa,mBAAmB,YAAY,OACzC,aAAa,mBAAmB,CAAC,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,GAC5D,IAAI,KAAK,AAAC,aAAa,mBAAmB,CAAS,OAAO,GAAG,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,GAC9F;oBACN,UAAU,aAAa,QAAQ,IAAI;oBACnC,OAAO,aAAa,KAAK,IAAI;gBAC/B;gBAEA,cAAc;gBACd,MAAM,WAAW,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,UAAU,aAAa,OAAO;gBACpE,IAAI,SAAS,MAAM,IAAI;oBACrB,SAAS;wBAAE,IAAI,SAAS,EAAE;wBAAE,GAAG,SAAS,IAAI,EAAE;oBAAC;gBACjD;YACF,OAAO;gBACL,OAAO,IAAI,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,OAAO,IAAI,CAAC;QACd,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,oBAAoB,OAAO;QAC/B,EAAE,cAAc;QAEhB,IAAI,CAAC,UAAU;QAEf,IAAI;YACF,cAAc;YAEd,MAAM,CAAA,GAAA,iKAAA,CAAA,YAAS,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,aAAa,aAAa;gBAChD,sBAAsB,UAAU,oBAAoB;gBACpD,cAAc,UAAU,YAAY;gBACpC,qBAAqB,UAAU,mBAAmB;gBAClD,iBAAiB,UAAU,eAAe;gBAC1C,qBAAqB,iKAAA,CAAA,YAAS,CAAC,QAAQ,CAAC,IAAI,KAAK,UAAU,mBAAmB;gBAC9E,UAAU,UAAU,QAAQ;gBAC5B,OAAO,UAAU,KAAK;gBACtB,MAAM,UAAU,YAAY;gBAC5B,aAAa,UAAU,mBAAmB;gBAC1C,WAAW,iKAAA,CAAA,YAAS,CAAC,GAAG;YAC1B;YAEA,MAAM;YACN,MAAM,qBAAqB,eAAe;QAE5C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM;QACR,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,mBAAmB,OACvB,MACA;QAEA,IAAI,CAAC,UAAU;QAEf,IAAI;YACF,kBAAkB,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,aAAa,EAAE;gBAAK,CAAC;YAE5D,MAAM,UAAU,MAAM,0HAAA,CAAA,iBAAc,CAAC,sBAAsB,CAAC,MAAM,YAAY;YAE9E,mBAAmB;YACnB,MAAM,YAAY,GAAG,aAAa,OAAO,CAAC;YAC1C,YAAY,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,UAAU,EAAE;gBAAQ,CAAC;YAEtD,8BAA8B;YAC9B,MAAM,CAAA,GAAA,iKAAA,CAAA,YAAS,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,aAAa,aAAa;gBAChD,CAAC,GAAG,aAAa,OAAO,CAAC,CAAC,EAAE;gBAC5B,WAAW,iKAAA,CAAA,YAAS,CAAC,GAAG;YAC1B;YAEA,MAAM;QAER,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM;QACR,SAAU;YACR,kBAAkB,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,aAAa,EAAE;gBAAM,CAAC;QAC/D;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI,CAAC,UAAU;QAEf,IAAI;YACF,MAAM,YAAY,GAAG,aAAa,OAAO,CAAC;YAC1C,MAAM,aAAa,QAAQ,CAAC,UAAU;YAEtC,IAAI,YAAY;gBACd,sBAAsB;gBACtB,MAAM,0HAAA,CAAA,iBAAc,CAAC,UAAU,CAAC;YAClC;YAEA,mBAAmB;YACnB,YAAY,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,UAAU,EAAE;gBAAG,CAAC;YAEjD,kBAAkB;YAClB,MAAM,CAAA,GAAA,iKAAA,CAAA,YAAS,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,aAAa,aAAa;gBAChD,CAAC,GAAG,aAAa,OAAO,CAAC,CAAC,EAAE;gBAC5B,WAAW,iKAAA,CAAA,YAAS,CAAC,GAAG;YAC1B;YAEA,MAAM;QAER,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,MAAM;QACR;IACF;IAEA,MAAM,oBAAoB,OAAO;QAC/B,EAAE,cAAc;QAEhB,IAAI,CAAC,UAAU;QAEf,IAAI;YACF,cAAc;YAEd,MAAM,CAAA,GAAA,iKAAA,CAAA,YAAS,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,aAAa,aAAa;gBAChD,gBAAgB,SAAS,cAAc;gBACvC,6BAA6B,SAAS,2BAA2B;gBACjE,oBAAoB,SAAS,kBAAkB;gBAC/C,uBAAuB,SAAS,qBAAqB;gBACrD,4BAA4B,SAAS,0BAA0B;gBAC/D,2BAA2B,SAAS,yBAAyB;gBAC7D,gBAAgB;gBAChB,WAAW,iKAAA,CAAA,YAAS,CAAC,GAAG;YAC1B;YAEA,MAAM;YACN,MAAM,qBAAqB,eAAe;QAE5C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM;QACR,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,wBAAwB;QAC5B,IAAI,CAAC,UAAU;QAEf,IAAI,CAAC,SAAS,cAAc,IAAI,CAAC,SAAS,cAAc,EAAE;YACxD,MAAM;YACN;QACF;QAEA,IAAI,CAAC,SAAS,cAAc,IAAI,CAAC,SAAS,2BAA2B,IAAI,CAAC,SAAS,kBAAkB,EAAE;YACrG,MAAM;YACN;QACF;QAEA,MAAM,iBAAiB,OAAO,OAAO,CACnC;QAGF,IAAI,CAAC,gBAAgB;QAErB,IAAI;YACF,cAAc;YAEd,MAAM,CAAA,GAAA,iKAAA,CAAA,YAAS,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,aAAa,aAAa;gBAChD,QAAQ;gBACR,UAAU;gBACV,aAAa,iKAAA,CAAA,YAAS,CAAC,GAAG;gBAC1B,WAAW,iKAAA,CAAA,YAAS,CAAC,GAAG;YAC1B;YAEA,MAAM;YACN,MAAM,qBAAqB,kCAAkC;QAE/D,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,MAAM;QACR,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,yBAAyB;QAC7B,IAAI,CAAC,YAAY,CAAC,OAAO;QAEzB,MAAM,gBAAgB,OAAO,OAAO,CAClC;QAGF,IAAI,CAAC,eAAe;QAEpB,IAAI;YACF,cAAc;YAEd,+CAA+C;YAC/C,MAAM,iBAAiB,SAAS,eAAe,IAAI;YACnD,MAAM,eAAe,eAAe,KAAK,CAAC;YAC1C,MAAM,eAAe,SAAS,YAAY,CAAC,EAAE,KAAK;YAClD,MAAM,eAAe,SAAS,YAAY,CAAC,EAAE,KAAK;YAClD,MAAM,aAAa,GAAG,aAAa,CAAC,EAAE,eAAe,GAAG;YAExD,MAAM,kBAAkB;gBACtB,MAAM,SAAS,IAAI;gBACnB,aAAa,SAAS,WAAW;gBACjC,SAAS,SAAS,OAAO;gBACzB,UAAU,SAAS,QAAQ;gBAC3B,OAAO,SAAS,KAAK;gBACrB,sBAAsB,SAAS,oBAAoB;gBACnD,cAAc,SAAS,YAAY;gBACnC,qBAAqB,SAAS,mBAAmB;gBACjD,iBAAiB;gBACjB,qBAAqB,iKAAA,CAAA,YAAS,CAAC,GAAG;gBAClC,QAAQ;gBACR,gBAAgB;gBAChB,gBAAgB;gBAChB,UAAU;gBACV,UAAU,CAAC;gBACX,sEAAsE;gBACtE,gBAAgB,SAAS,cAAc,IAAI;gBAC3C,6BAA6B,SAAS,2BAA2B,IAAI;gBACrE,oBAAoB,SAAS,kBAAkB,IAAI;gBACnD,mDAAmD;gBACnD,uBAAuB;gBACvB,4BAA4B;gBAC5B,2BAA2B;gBAC3B,WAAW,iKAAA,CAAA,YAAS,CAAC,GAAG;gBACxB,WAAW,iKAAA,CAAA,YAAS,CAAC,GAAG;gBACxB,iCAAiC;gBACjC,mBAAmB;gBACnB,gBAAgB,SAAS,eAAe;YAC1C;YAEA,MAAM,SAAS,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,cAAc;YAEzD,MAAM,CAAC,YAAY,EAAE,WAAW,sBAAsB,CAAC;YACvD,OAAO,IAAI,CAAC,CAAC,iBAAiB,EAAE,OAAO,EAAE,CAAC,KAAK,CAAC;QAElD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,MAAM;QACR,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI,CAAC,UAAU;QAEf,IAAI;YACF,cAAc;YAEd,MAAM,CAAA,GAAA,iKAAA,CAAA,YAAS,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,aAAa,aAAa;gBAChD,gBAAgB,SAAS,cAAc;gBACvC,6BAA6B,SAAS,2BAA2B;gBACjE,oBAAoB,SAAS,kBAAkB;gBAC/C,uBAAuB,SAAS,qBAAqB;gBACrD,4BAA4B,SAAS,0BAA0B;gBAC/D,2BAA2B,SAAS,yBAAyB;gBAC7D,QAAQ;gBACR,WAAW,iKAAA,CAAA,YAAS,CAAC,GAAG;YAC1B;YAEA,MAAM;QAER,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM;QACR,SAAU;YACR,cAAc;QAChB;IACF;IAEA,IAAI,WAAW,aAAa;QAC1B,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BAAU;;;;;;;;;;;IAG/B;IAEA,IAAI,CAAC,WAAW,CAAC,UAAU;QACzB,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,yHAAA,CAAA,UAAU;;;;;0BAEX,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DACX,SAAS,MAAM,KAAK,cAAc,kBAAkB;;;;;;0DAEvD,8OAAC;gDAAE,WAAU;;oDACV,SAAS,YAAY;oDAAC;oDAAI,OAAO;;;;;;;0DAEpC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAW,CAAC,2CAA2C,EAC3D,SAAS,MAAM,KAAK,cAChB,sEACA,yEACJ;kEACC,SAAS,MAAM,KAAK,cAAc,cAAc;;;;;;kEAEnD,8OAAC;wDAAK,WAAU;;4DAA2C;4DAChD,SAAS,eAAe;;;;;;;oDAElC,SAAS,iBAAiB,kBACzB,8OAAC;wDAAK,WAAU;;4DAA2C;4DAC9C,SAAS,cAAc;;;;;;;;;;;;;4CAIvC,SAAS,MAAM,KAAK,6BACnB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAE,WAAU;;sEACX,8OAAC;sEAAO;;;;;;wDAAc;;;;;;;;;;;;;;;;;;kDAK9B,8OAAC;wCAAI,WAAU;;4CACZ,SAAS,MAAM,KAAK,6BACnB,8OAAC;gDACC,SAAS;gDACT,UAAU;gDACV,WAAU;;oDAET,4BACC,8OAAC;wDAAI,WAAU;wDAA6C,OAAM;wDAA6B,MAAK;wDAAO,SAAQ;;0EACjH,8OAAC;gEAAO,WAAU;gEAAa,IAAG;gEAAK,IAAG;gEAAK,GAAE;gEAAK,QAAO;gEAAe,aAAY;;;;;;0EACxF,8OAAC;gEAAK,WAAU;gEAAa,MAAK;gEAAe,GAAE;;;;;;;;;;;;kEAGvD,8OAAC;kEAAM,aAAa,gBAAgB;;;;;;;;;;;;0DAGxC,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;sCAQP,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAW,CAAC,kBAAkB,EAAE,SAAS,cAAc,GAAG,uCAAuC,oCAAoC;;0DACxI,8OAAC;gDAAI,WAAW,CAAC,yCAAyC,EAAE,SAAS,cAAc,GAAG,mCAAmC,+BAA+B,4CAA4C,CAAC;0DAClM,SAAS,cAAc,GAAG,MAAM;;;;;;0DAEnC,8OAAC;gDAAK,WAAU;0DAA2B;;;;;;;;;;;;kDAE7C,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAW,CAAC,kBAAkB,EAAE,SAAS,cAAc,GAAG,uCAAuC,oCAAoC;;0DACxI,8OAAC;gDAAI,WAAW,CAAC,yCAAyC,EAAE,SAAS,cAAc,GAAG,mCAAmC,+BAA+B,4CAA4C,CAAC;0DAClM,SAAS,cAAc,GAAG,MAAM;;;;;;0DAEnC,8OAAC;gDAAK,WAAU;0DAA2B;;;;;;;;;;;;;;;;;;;;;;;sCAMjD,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS,IAAM,eAAe;wCAC9B,UAAU,SAAS,MAAM,KAAK;wCAC9B,WAAW,CAAC,kEAAkE,EAC5E,gBAAgB,UACZ,mEACA,gFACL,CAAC,EAAE,SAAS,MAAM,KAAK,cAAc,kCAAkC,IAAI;kDAE3E,SAAS,MAAM,KAAK,cAAc,2BAA2B;;;;;;kDAEhE,8OAAC;wCACC,SAAS,IAAM,eAAe;wCAC9B,UAAU,SAAS,MAAM,KAAK;wCAC9B,WAAW,CAAC,kEAAkE,EAC5E,gBAAgB,UACZ,mEACA,gFACL,CAAC,EAAE,SAAS,MAAM,KAAK,cAAc,kCAAkC,IAAI;kDAE3E,SAAS,MAAM,KAAK,cAAc,mBAAmB;;;;;;;;;;;;;;;;;wBAM3D,gBAAgB,yBACf,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAK,UAAU;gCAAmB,WAAU;;kDAC3C,8OAAC;wCAAG,WAAU;kDACX,SAAS,MAAM,KAAK,cAAc,kCAAkC;;;;;;kDAIvE,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAAkE;;;;;;0DAGnF,8OAAC;gDACC,MAAK;gDACL,OAAO,UAAU,oBAAoB;gDACrC,UAAU,CAAC,IAAM,aAAa,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,sBAAsB,EAAE,MAAM,CAAC,KAAK;wDAAC,CAAC;gDACxF,UAAU,SAAS,MAAM,KAAK;gDAC9B,WAAU;gDACV,QAAQ;;;;;;;;;;;;kDAKZ,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAAkE;;;;;;0DAGnF,8OAAC;gDACC,MAAK;gDACL,OAAO,UAAU,YAAY;gDAC7B,UAAU,CAAC,IAAM,aAAa,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,cAAc,EAAE,MAAM,CAAC,KAAK;wDAAC,CAAC;gDAChF,UAAU,SAAS,MAAM,KAAK;gDAC9B,WAAU;gDACV,QAAQ;;;;;;;;;;;;kDAKZ,8OAAC;;0DACC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAM,WAAU;kEAA6D;;;;;;kEAG9E,8OAAC;wDACC,MAAK;wDACL,SAAS,IAAM,mBAAmB,CAAC;wDACnC,WAAU;kEAET,kBAAkB,wBAAwB;;;;;;;;;;;;4CAG9C,gCACC,8OAAC,mIAAA,CAAA,UAAoB;gDACnB,OAAO,UAAU,mBAAmB;gDACpC,UAAU,CAAC,QAAU,aAAa,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,qBAAqB;wDAAM,CAAC;gDAClF,aAAY;gDACZ,WAAU;gDACV,UAAU,SAAS,MAAM,KAAK;;;;;qEAGhC,8OAAC,6HAAA,CAAA,UAAc;gDACb,OAAO,UAAU,mBAAmB;gDACpC,UAAU,CAAC,QAAU,aAAa,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,qBAAqB;wDAAM,CAAC;gDAClF,aAAY;gDACZ,WAAU;gDACV,UAAU,SAAS,MAAM,KAAK;;;;;;;;;;;;kDAMpC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAAkE;;;;;;kEAGnF,8OAAC;wDACC,MAAK;wDACL,OAAO,UAAU,eAAe;wDAChC,UAAU,CAAC,IAAM,aAAa,CAAA,OAAQ,CAAC;oEAAE,GAAG,IAAI;oEAAE,iBAAiB,EAAE,MAAM,CAAC,KAAK;gEAAC,CAAC;wDACnF,UAAU,SAAS,MAAM,KAAK;wDAC9B,WAAU;wDACV,QAAQ;;;;;;;;;;;;0DAIZ,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAAkE;;;;;;kEAGnF,8OAAC;wDACC,MAAK;wDACL,OAAO,UAAU,mBAAmB;wDACpC,UAAU,CAAC,IAAM,aAAa,CAAA,OAAQ,CAAC;oEAAE,GAAG,IAAI;oEAAE,qBAAqB,EAAE,MAAM,CAAC,KAAK;gEAAC,CAAC;wDACvF,UAAU,SAAS,MAAM,KAAK;wDAC9B,WAAU;wDACV,QAAQ;;;;;;;;;;;;;;;;;;kDAMd,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAAkE;;;;;;kEAGnF,8OAAC;wDACC,OAAO,UAAU,QAAQ;wDACzB,UAAU,CAAC,IAAM,aAAa,CAAA,OAAQ,CAAC;oEAAE,GAAG,IAAI;oEAAE,UAAU,EAAE,MAAM,CAAC,KAAK;gEAAC,CAAC;wDAC5E,UAAU,SAAS,MAAM,KAAK;wDAC9B,WAAU;;0EAEV,8OAAC;gEAAO,OAAM;0EAAoB;;;;;;0EAClC,8OAAC;gEAAO,OAAM;0EAAqB;;;;;;0EACnC,8OAAC;gEAAO,OAAM;0EAAwB;;;;;;0EACtC,8OAAC;gEAAO,OAAM;0EAAkB;;;;;;0EAChC,8OAAC;gEAAO,OAAM;0EAAqB;;;;;;;;;;;;;;;;;;0DAIvC,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAAkE;;;;;;kEAGnF,8OAAC;wDACC,MAAK;wDACL,KAAI;wDACJ,MAAK;wDACL,OAAO,UAAU,KAAK;wDACtB,UAAU,CAAC,IAAM,aAAa,CAAA,OAAQ,CAAC;oEAAE,GAAG,IAAI;oEAAE,OAAO,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;gEAAE,CAAC;wDAC1F,UAAU,SAAS,MAAM,KAAK;wDAC9B,WAAU;;;;;;;;;;;;;;;;;;oCAMf,SAAS,MAAM,KAAK,6BACnB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,MAAK;4CACL,UAAU;4CACV,WAAU;sDAET,aAAa,gBAAgB;;;;;;;;;;;;;;;;;;;;;;wBASzC,gBAAgB,yBACf,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAK,UAAU;gCAAmB,WAAU;;kDAC3C,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DACX,SAAS,MAAM,KAAK,cAAc,+BAA+B;;;;;;0DAEpE,8OAAC;gDAAE,WAAU;0DACV,SAAS,MAAM,KAAK,cACjB,kFACA;;;;;;;;;;;;kDAMR,8OAAC;;0DACC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAM,WAAU;kEAA6D;;;;;;kEAG9E,8OAAC;wDACC,MAAK;wDACL,SAAS,IAAM,mBAAmB,CAAC;wDACnC,WAAU;kEAET,kBAAkB,wBAAwB;;;;;;;;;;;;0DAG/C,8OAAC;gDAAE,WAAU;0DAAgD;;;;;;0DAK7D,8OAAC;gDAAI,WAAU;0DACZ,gCACC,8OAAC,mIAAA,CAAA,UAAoB;oDACnB,OAAO,SAAS,cAAc;oDAC9B,UAAU,CAAC,QAAU,YAAY,CAAA,OAAQ,CAAC;gEAAE,GAAG,IAAI;gEAAE,gBAAgB;4DAAM,CAAC;oDAC5E,aAAY;oDACZ,WAAU;oDACV,UAAU,SAAS,MAAM,KAAK;;;;;yEAGhC,8OAAC,6HAAA,CAAA,UAAc;oDACb,OAAO,SAAS,cAAc;oDAC9B,UAAU,CAAC,QAAU,YAAY,CAAA,OAAQ,CAAC;gEAAE,GAAG,IAAI;gEAAE,gBAAgB;4DAAM,CAAC;oDAC5E,aAAY;oDACZ,WAAU;oDACV,UAAU,SAAS,MAAM,KAAK;;;;;;;;;;;0DAMpC,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAAkE;;;;;;kEAGnF,8OAAC,yHAAA,CAAA,UAAU;wDACT,cAAc,CAAC,OAAS,iBAAiB,MAAM;wDAC/C,cAAc,IAAM,iBAAiB;wDACrC,gBAAgB,SAAS,qBAAqB;wDAC9C,iBAAiB,SAAS,qBAAqB,GAAG,0HAAA,CAAA,iBAAc,CAAC,kBAAkB,CAAC,SAAS,qBAAqB,IAAI;wDACtH,UAAU,eAAe,cAAc,IAAI,SAAS,MAAM,KAAK;wDAC/D,WAAU;;;;;;;;;;;;;;;;;;kDAMhB,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAAkE;;;;;;0DAGnF,8OAAC;gDAAE,WAAU;0DAAgD;;;;;;0DAK7D,8OAAC;gDAAI,WAAU;0DACZ,gCACC,8OAAC,mIAAA,CAAA,UAAoB;oDACnB,OAAO,SAAS,2BAA2B;oDAC3C,UAAU,CAAC,QAAU,YAAY,CAAA,OAAQ,CAAC;gEAAE,GAAG,IAAI;gEAAE,6BAA6B;4DAAM,CAAC;oDACzF,aAAY;oDACZ,WAAU;oDACV,UAAU,SAAS,MAAM,KAAK;;;;;yEAGhC,8OAAC,6HAAA,CAAA,UAAc;oDACb,OAAO,SAAS,2BAA2B;oDAC3C,UAAU,CAAC,QAAU,YAAY,CAAA,OAAQ,CAAC;gEAAE,GAAG,IAAI;gEAAE,6BAA6B;4DAAM,CAAC;oDACzF,aAAY;oDACZ,WAAU;oDACV,UAAU,SAAS,MAAM,KAAK;;;;;;;;;;;0DAMpC,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAAkE;;;;;;kEAGnF,8OAAC,yHAAA,CAAA,UAAU;wDACT,cAAc,CAAC,OAAS,iBAAiB,MAAM;wDAC/C,cAAc,IAAM,iBAAiB;wDACrC,gBAAgB,SAAS,0BAA0B;wDACnD,iBAAiB,SAAS,0BAA0B,GAAG,0HAAA,CAAA,iBAAc,CAAC,kBAAkB,CAAC,SAAS,0BAA0B,IAAI;wDAChI,UAAU,eAAe,mBAAmB,IAAI,SAAS,MAAM,KAAK;wDACpE,WAAU;;;;;;;;;;;;;;;;;;kDAMhB,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAAkE;;;;;;0DAGnF,8OAAC;gDAAE,WAAU;0DAAgD;;;;;;0DAK7D,8OAAC;gDAAI,WAAU;0DACZ,gCACC,8OAAC,mIAAA,CAAA,UAAoB;oDACnB,OAAO,SAAS,kBAAkB;oDAClC,UAAU,CAAC,QAAU,YAAY,CAAA,OAAQ,CAAC;gEAAE,GAAG,IAAI;gEAAE,oBAAoB;4DAAM,CAAC;oDAChF,aAAY;oDACZ,WAAU;oDACV,UAAU,SAAS,MAAM,KAAK;;;;;yEAGhC,8OAAC,6HAAA,CAAA,UAAc;oDACb,OAAO,SAAS,kBAAkB;oDAClC,UAAU,CAAC,QAAU,YAAY,CAAA,OAAQ,CAAC;gEAAE,GAAG,IAAI;gEAAE,oBAAoB;4DAAM,CAAC;oDAChF,aAAY;oDACZ,WAAU;oDACV,UAAU,SAAS,MAAM,KAAK;;;;;;;;;;;0DAMpC,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAAkE;;;;;;kEAGnF,8OAAC,yHAAA,CAAA,UAAU;wDACT,cAAc,CAAC,OAAS,iBAAiB,MAAM;wDAC/C,cAAc,IAAM,iBAAiB;wDACrC,gBAAgB,SAAS,yBAAyB;wDAClD,iBAAiB,SAAS,yBAAyB,GAAG,0HAAA,CAAA,iBAAc,CAAC,kBAAkB,CAAC,SAAS,yBAAyB,IAAI;wDAC9H,UAAU,eAAe,kBAAkB,IAAI,SAAS,MAAM,KAAK;wDACnE,WAAU;;;;;;;;;;;;;;;;;;oCAMf,SAAS,MAAM,KAAK,6BACnB,8OAAC;wCAAI,WAAU;;0DACf,8OAAC;gDACC,MAAK;gDACL,SAAS;gDACT,UAAU;gDACV,WAAU;;oDAET,4BACC,8OAAC;wDAAI,WAAU;wDAA6C,OAAM;wDAA6B,MAAK;wDAAO,SAAQ;;0EACjH,8OAAC;gEAAO,WAAU;gEAAa,IAAG;gEAAK,IAAG;gEAAK,GAAE;gEAAK,QAAO;gEAAe,aAAY;;;;;;0EACxF,8OAAC;gEAAK,WAAU;gEAAa,MAAK;gEAAe,GAAE;;;;;;;;;;;;kEAGvD,8OAAC;kEAAM,aAAa,cAAc;;;;;;;;;;;;0DAGpC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,MAAK;wDACL,UAAU;wDACV,WAAU;;4DAET,4BACC,8OAAC;gEAAI,WAAU;gEAA6C,OAAM;gEAA6B,MAAK;gEAAO,SAAQ;;kFACjH,8OAAC;wEAAO,WAAU;wEAAa,IAAG;wEAAK,IAAG;wEAAK,GAAE;wEAAK,QAAO;wEAAe,aAAY;;;;;;kFACxF,8OAAC;wEAAK,WAAU;wEAAa,MAAK;wEAAe,GAAE;;;;;;;;;;;;0EAGvD,8OAAC;0EAAM,aAAa,cAAc;;;;;;;;;;;;oDAGnC,SAAS,cAAc,kBACtB,8OAAC;wDACC,MAAK;wDACL,SAAS;wDACT,UAAU,cAAc,SAAS,MAAM,KAAK;wDAC5C,WAAU;;4DAET,cAAc,SAAS,MAAM,KAAK,6BACjC,8OAAC;gEAAI,WAAU;gEAA6C,OAAM;gEAA6B,MAAK;gEAAO,SAAQ;;kFACjH,8OAAC;wEAAO,WAAU;wEAAa,IAAG;wEAAK,IAAG;wEAAK,GAAE;wEAAK,QAAO;wEAAe,aAAY;;;;;;kFACxF,8OAAC;wEAAK,WAAU;wEAAa,MAAK;wEAAe,GAAE;;;;;;;;;;;;0EAGvD,8OAAC;0EAAM,SAAS,MAAM,KAAK,cAAc,cAAc,aAAa,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAa9G", "debugId": null}}]}