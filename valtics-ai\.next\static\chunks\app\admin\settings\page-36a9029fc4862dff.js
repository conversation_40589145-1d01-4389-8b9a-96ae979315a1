(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[122],{288:(e,t,a)=>{Promise.resolve().then(a.bind(a,6331))},477:()=>{},6331:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>f});var r=a(5155),n=a(2115),i=a(3274),s=a(5695),o=a(1573),l=a(5317),c=a(1138),d=a(6281),u=a(9882);class p{static initializeClaude(e){this.claudeClient=new d.Ay({apiKey:e})}static initializeOpenAI(e){this.openaiClient=new u.Ay({apiKey:e})}static async testClaudeConnection(e){try{let t=new d.Ay({apiKey:e}),a=await t.messages.create({model:"claude-3-haiku-20240307",max_tokens:10,messages:[{role:"user",content:'Test connection. Respond with "OK".'}]});return"text"===a.content[0].type&&a.content[0].text.includes("OK")}catch(e){return console.error("Claude connection test failed:",e),!1}}static async testOpenAIConnection(e){try{var t,a,r;let n=new u.Ay({apiKey:e});return(null==(r=(await n.chat.completions.create({model:"gpt-3.5-turbo",max_tokens:10,messages:[{role:"user",content:'Test connection. Respond with "OK".'}]})).choices[0])||null==(a=r.message)||null==(t=a.content)?void 0:t.includes("OK"))||!1}catch(e){return console.error("OpenAI connection test failed:",e),!1}}static async generateWithClaude(e,t,a,r){if(!this.claudeClient)throw Error("Claude client not initialized");try{let n=this.buildFullPrompt(e,t,a,r),i=await this.claudeClient.messages.create({model:"claude-3-sonnet-20240229",max_tokens:4e3,temperature:.7,messages:[{role:"user",content:n}]});if("text"!==i.content[0].type)throw Error("Unexpected response format from Claude");return this.parseAIResponse(i.content[0].text)}catch(e){throw console.error("Claude generation failed:",e),Error("Claude API error: ".concat(e instanceof Error?e.message:"Unknown error"))}}static async generateWithOpenAI(e,t,a,r){if(!this.openaiClient)throw Error("OpenAI client not initialized");try{var n,i;let s=this.buildFullPrompt(e,t,a,r),o=null==(i=(await this.openaiClient.chat.completions.create({model:"gpt-4",max_tokens:4e3,temperature:.7,messages:[{role:"system",content:"You are an expert business analyst creating professional Business Value Assessment artifacts."},{role:"user",content:s}]})).choices[0])||null==(n=i.message)?void 0:n.content;if(!o)throw Error("No content received from OpenAI");return this.parseAIResponse(o)}catch(e){throw console.error("OpenAI generation failed:",e),Error("OpenAI API error: ".concat(e instanceof Error?e.message:"Unknown error"))}}static buildFullPrompt(e,t,a,r){return"".concat(e,"\n\nDOCUMENT CONTENTS:\n\nEnterprise Need Document:\n").concat(t.enterpriseNeed,"\n\nSolution Description Document:\n").concat(t.solution,"\n\nRisk of No Investment Document:\n").concat(t.risk,"\n\nPlease generate exactly three separate artifacts as requested. Each artifact should be formatted as markdown and clearly separated. Start each artifact with a clear header indicating which one it is (Enterprise Need, Proposed Solution, or Risk of No Investment).")}static parseAIResponse(e){let t=e.split(/(?=#{1,3}\s*(?:Enterprise Need|Proposed Solution|Risk of No Investment))/i),a="",r="",n="";for(let e of t){let t=e.trim();t&&(t.toLowerCase().includes("enterprise need")?a=t:t.toLowerCase().includes("proposed solution")?r=t:t.toLowerCase().includes("risk of no investment")&&(n=t))}if(!a||!r||!n){let t=e.split(/\n\s*\n/);t.length>=3?(a=t[0]||"Enterprise Need artifact could not be parsed.",r=t[1]||"Solution artifact could not be parsed.",n=t[2]||"Risk artifact could not be parsed."):(a="# Enterprise Need Artifact\n\n".concat(e),r="# Proposed Solution Artifact\n\n".concat(e),n="# Risk of No Investment Artifact\n\n".concat(e))}return{enterpriseNeedArtifact:a.trim(),solutionArtifact:r.trim(),riskArtifact:n.trim()}}static async generateArtifactsWithRetry(e,t,a,r,n){let i=arguments.length>5&&void 0!==arguments[5]?arguments[5]:3,s=null;for(let o=1;o<=i;o++)try{if("claude"===e)return await this.generateWithClaude(t,a,r,n);return await this.generateWithOpenAI(t,a,r,n)}catch(e){s=e instanceof Error?e:Error("Unknown error"),console.error("AI generation attempt ".concat(o," failed:"),s),o<i&&await new Promise(e=>setTimeout(e,1e3*Math.pow(2,o)))}throw Error("AI generation failed after ".concat(i," attempts: ").concat(null==s?void 0:s.message))}}p.claudeClient=null,p.openaiClient=null;var y=a(2109),h=a.n(y),A=a(9509);let m="main",g=A.env.NEXT_PUBLIC_ENCRYPTION_KEY||"valtics-ai-default-key";class x{static encryptApiKey(e){return h().AES.encrypt(e,g).toString()}static decryptApiKey(e){return h().AES.decrypt(e,g).toString(h().enc.Utf8)}static async getAPIConfiguration(){try{var e,t,a;let r=await (0,l.x7)((0,l.H9)(c.db,"apiConfig",m));if(!r.exists())return null;let n=r.data();return{id:r.id,claudeApiKey:n.claudeApiKey?this.decryptApiKey(n.claudeApiKey):void 0,openaiApiKey:n.openaiApiKey?this.decryptApiKey(n.openaiApiKey):void 0,createdAt:(null==(e=n.createdAt)?void 0:e.toDate())||new Date,updatedAt:(null==(t=n.updatedAt)?void 0:t.toDate())||new Date,claudeStatus:n.claudeStatus||"untested",openaiStatus:n.openaiStatus||"untested",lastTestedAt:null==(a=n.lastTestedAt)?void 0:a.toDate()}}catch(e){throw console.error("Error fetching API configuration:",e),Error("Failed to fetch API configuration")}}static async saveAPIConfiguration(e){try{let t=(0,l.H9)(c.db,"apiConfig",m),a=await (0,l.x7)(t),r={updatedAt:l.Dc.now()};e.claudeApiKey&&(r.claudeApiKey=this.encryptApiKey(e.claudeApiKey),r.claudeStatus="untested"),e.openaiApiKey&&(r.openaiApiKey=this.encryptApiKey(e.openaiApiKey),r.openaiStatus="untested"),a.exists()?await (0,l.mZ)(t,r):await (0,l.BN)(t,{...r,createdAt:l.Dc.now()})}catch(e){throw console.error("Error saving API configuration:",e),Error("Failed to save API configuration")}}static async testAPIConnections(){try{let e=await this.getAPIConfiguration();if(!e)return{claudeStatus:"untested",openaiStatus:"untested"};let t={claudeStatus:"untested",openaiStatus:"untested"};if(e.claudeApiKey)try{t.claudeStatus=await p.testClaudeConnection(e.claudeApiKey)?"connected":"error"}catch(e){console.error("Claude connection test failed:",e),t.claudeStatus="error"}if(e.openaiApiKey)try{t.openaiStatus=await p.testOpenAIConnection(e.openaiApiKey)?"connected":"error"}catch(e){console.error("OpenAI connection test failed:",e),t.openaiStatus="error"}return await (0,l.mZ)((0,l.H9)(c.db,"apiConfig",m),{claudeStatus:t.claudeStatus,openaiStatus:t.openaiStatus,lastTestedAt:l.Dc.now(),updatedAt:l.Dc.now()}),t}catch(e){throw console.error("Error testing API connections:",e),Error("Failed to test API connections")}}static async getDecryptedAPIKeys(){try{let e=await this.getAPIConfiguration();return{claudeApiKey:null==e?void 0:e.claudeApiKey,openaiApiKey:null==e?void 0:e.openaiApiKey}}catch(e){throw console.error("Error getting decrypted API keys:",e),Error("Failed to get API keys")}}static async areAPIKeysConfigured(){try{let e=await this.getAPIConfiguration(),t=!!(null==e?void 0:e.claudeApiKey),a=!!(null==e?void 0:e.openaiApiKey);return{claudeConfigured:t,openaiConfigured:a,anyConfigured:t||a}}catch(e){return console.error("Error checking API key configuration:",e),{claudeConfigured:!1,openaiConfigured:!1,anyConfigured:!1}}}static async initializeAIServices(){try{let e=await this.getDecryptedAPIKeys();e.claudeApiKey&&p.initializeClaude(e.claudeApiKey),e.openaiApiKey&&p.initializeOpenAI(e.openaiApiKey)}catch(e){throw console.error("Error initializing AI services:",e),Error("Failed to initialize AI services")}}static validateAPIKey(e,t){if(!t||0===t.trim().length)return{isValid:!1,error:"API key cannot be empty"};if("claude"===e){if(!t.startsWith("sk-ant-"))return{isValid:!1,error:'Claude API key should start with "sk-ant-"'}}else if("openai"===e&&!t.startsWith("sk-"))return{isValid:!1,error:'OpenAI API key should start with "sk-"'};return t.length<20?{isValid:!1,error:"API key appears to be too short"}:{isValid:!0}}}function f(){let{user:e,loading:t,isAdmin:a}=(0,i.A)(),l=(0,s.useRouter)(),[c,d]=(0,n.useState)(null),[u,p]=(0,n.useState)(!0),[y,h]=(0,n.useState)(!1),[A,m]=(0,n.useState)(!1),[g,f]=(0,n.useState)(!1),[w,v]=(0,n.useState)({claudeApiKey:"",openaiApiKey:""}),[I,b]=(0,n.useState)({});(0,n.useEffect)(()=>{if(!t&&!a)return void l.push("/");a&&k()},[t,a,l]);let k=async()=>{try{p(!0);let e=await x.getAPIConfiguration();d(e),e&&(v({claudeApiKey:e.claudeApiKey||"",openaiApiKey:e.openaiApiKey||""}),b({claudeStatus:e.claudeStatus,openaiStatus:e.openaiStatus}))}catch(e){console.error("Error fetching API config:",e),alert("Error loading API configuration")}finally{p(!1)}},C=async()=>{try{if(h(!0),w.claudeApiKey){let e=x.validateAPIKey("claude",w.claudeApiKey);if(!e.isValid)return void alert("Claude API Key Error: ".concat(e.error))}if(w.openaiApiKey){let e=x.validateAPIKey("openai",w.openaiApiKey);if(!e.isValid)return void alert("OpenAI API Key Error: ".concat(e.error))}await x.saveAPIConfiguration({claudeApiKey:w.claudeApiKey||void 0,openaiApiKey:w.openaiApiKey||void 0}),alert("API configuration saved successfully!"),await k()}catch(e){console.error("Error saving API config:",e),alert("Error saving API configuration")}finally{h(!1)}},K=async()=>{try{m(!0);let e=await x.testAPIConnections();b(e);let t=[];"connected"===e.claudeStatus?t.push("Claude API: Connected ✓"):"error"===e.claudeStatus&&t.push("Claude API: Connection failed ✗"),"connected"===e.openaiStatus?t.push("OpenAI API: Connected ✓"):"error"===e.openaiStatus&&t.push("OpenAI API: Connection failed ✗"),0===t.length?alert("No API keys configured to test"):alert(t.join("\n"))}catch(e){console.error("Error testing connections:",e),alert("Error testing API connections")}finally{m(!1)}},N=e=>{switch(e){case"connected":return(0,r.jsx)("span",{className:"text-green-500",children:"✓ Connected"});case"error":return(0,r.jsx)("span",{className:"text-red-500",children:"✗ Error"});default:return(0,r.jsx)("span",{className:"text-gray-500",children:"○ Untested"})}};return t||u?(0,r.jsx)("div",{className:"flex min-h-screen items-center justify-center",children:(0,r.jsx)("div",{className:"text-xl",children:"Loading..."})}):a?(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[(0,r.jsx)(o.A,{}),(0,r.jsx)("div",{className:"max-w-4xl mx-auto py-6 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"px-4 py-6 sm:px-0",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"Admin Settings"}),(0,r.jsx)("p",{className:"mt-2 text-gray-600 dark:text-gray-300",children:"Configure API keys and system settings for VALTICS AI."})]}),(0,r.jsx)("div",{className:"bg-white dark:bg-gray-800 shadow rounded-lg",children:(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:"AI API Configuration"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mt-1",children:"Configure API keys for Claude AI and OpenAI to enable AI artifact generation."})]}),(0,r.jsxs)("button",{onClick:()=>f(!g),className:"text-sm text-blue-600 dark:text-blue-400 hover:underline",children:[g?"Hide":"Show"," API Keys"]})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Claude AI API Key"}),N(I.claudeStatus)]}),(0,r.jsx)("input",{type:g?"text":"password",value:w.claudeApiKey,onChange:e=>v(t=>({...t,claudeApiKey:e.target.value})),placeholder:"sk-ant-...",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"}),(0,r.jsxs)("p",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:["Get your API key from ",(0,r.jsx)("a",{href:"https://console.anthropic.com/",target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 dark:text-blue-400 hover:underline",children:"Anthropic Console"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"OpenAI API Key"}),N(I.openaiStatus)]}),(0,r.jsx)("input",{type:g?"text":"password",value:w.openaiApiKey,onChange:e=>v(t=>({...t,openaiApiKey:e.target.value})),placeholder:"sk-...",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"}),(0,r.jsxs)("p",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:["Get your API key from ",(0,r.jsx)("a",{href:"https://platform.openai.com/api-keys",target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 dark:text-blue-400 hover:underline",children:"OpenAI Platform"})]})]}),(0,r.jsxs)("div",{className:"flex space-x-4 pt-4 border-t border-gray-200 dark:border-gray-700",children:[(0,r.jsxs)("button",{onClick:C,disabled:y,className:"bg-blue-600 dark:bg-blue-700 text-white px-6 py-2 rounded-md font-medium hover:bg-blue-700 dark:hover:bg-blue-800 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2",children:[y&&(0,r.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,r.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,r.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),(0,r.jsx)("span",{children:y?"Saving...":"Save Configuration"})]}),(0,r.jsxs)("button",{onClick:K,disabled:A||!w.claudeApiKey&&!w.openaiApiKey,className:"bg-green-600 dark:bg-green-700 text-white px-6 py-2 rounded-md font-medium hover:bg-green-700 dark:hover:bg-green-800 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2",children:[A&&(0,r.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,r.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,r.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),(0,r.jsx)("span",{children:A?"Testing...":"Test Connections"})]})]}),(null==c?void 0:c.lastTestedAt)&&(0,r.jsxs)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:["Last tested: ",c.lastTestedAt.toLocaleString()]})]})]})}),(0,r.jsx)("div",{className:"mt-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-md",children:(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("svg",{className:"h-5 w-5 text-yellow-400",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})})}),(0,r.jsxs)("div",{className:"ml-3",children:[(0,r.jsx)("h3",{className:"text-sm font-medium text-yellow-800 dark:text-yellow-200",children:"Security Notice"}),(0,r.jsx)("div",{className:"mt-2 text-sm text-yellow-700 dark:text-yellow-300",children:(0,r.jsx)("p",{children:"API keys are encrypted before storage and are only accessible to admin users. Keep your API keys secure and rotate them regularly. Never share your API keys with unauthorized users."})})]})]})})]})})]}):null}}},e=>{var t=t=>e(e.s=t);e.O(0,[992,965,288,874,63,973,573,441,684,358],()=>t(288)),_N_E=e.O()}]);