(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[698],{1469:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{default:function(){return n},getImageProps:function(){return i}});let s=a(8229),r=a(8883),d=a(3063),l=s._(a(1193));function i(e){let{props:t}=(0,r.getImgProps)(e,{defaultLoader:l.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,a]of Object.entries(t))void 0===a&&delete t[e];return{props:t}}let n=d.Image},5695:(e,t,a)=>{"use strict";var s=a(8999);a.o(s,"useParams")&&a.d(t,{useParams:function(){return s.useParams}}),a.o(s,"useRouter")&&a.d(t,{useRouter:function(){return s.useRouter}}),a.o(s,"useSearchParams")&&a.d(t,{useSearchParams:function(){return s.useSearchParams}})},6766:(e,t,a)=>{"use strict";a.d(t,{default:()=>r.a});var s=a(1469),r=a.n(s)},7161:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>g});var s=a(5155),r=a(2115),d=a(3274),l=a(5695),i=a(6874),n=a.n(i),c=a(5317),x=a(6203),m=a(1138),o=a(1573);function g(){let{user:e,loading:t,isAdmin:a}=(0,d.A)(),i=(0,l.useRouter)(),[g,u]=(0,r.useState)([]),[p,b]=(0,r.useState)([]),[y,j]=(0,r.useState)([]),[f,v]=(0,r.useState)([]),[N,w]=(0,r.useState)(!0),[k,A]=(0,r.useState)("overview"),[C,S]=(0,r.useState)(!1),[D,L]=(0,r.useState)({name:"",description:"",logoUrl:""}),[P,E]=(0,r.useState)(null),[I,U]=(0,r.useState)(!1),[B,M]=(0,r.useState)(""),[T,R]=(0,r.useState)("all"),[_,G]=(0,r.useState)("all");(0,r.useEffect)(()=>{t||e&&a||i.push("/dashboard")},[e,t,a,i]),(0,r.useEffect)(()=>{e&&a&&V()},[e,a]);let V=async()=>{try{w(!0);let e=(0,c.P)((0,c.rJ)(m.db,"brands"),(0,c.My)("name","asc")),t=(await (0,c.GG)(e)).docs.map(e=>({id:e.id,...e.data()}));u(t);let a=(0,c.P)((0,c.rJ)(m.db,"templates"),(0,c.My)("createdAt","desc")),s=(await (0,c.GG)(a)).docs.map(e=>({id:e.id,...e.data()}));b(s);let r=(0,c.P)((0,c.rJ)(m.db,"users"),(0,c.My)("createdAt","desc")),d=(await (0,c.GG)(r)).docs.map(e=>({id:e.id,...e.data()}));j(d);let l=(0,c.P)((0,c.rJ)(m.db,"bvaInstances"),(0,c.My)("createdAt","desc")),i=(await (0,c.GG)(l)).docs.map(e=>({id:e.id,...e.data()}));v(i)}catch(e){console.error("Error fetching admin data:",e)}finally{w(!1)}},O=async e=>{e.preventDefault();try{await (0,c.gS)((0,c.rJ)(m.db,"brands"),{...D,isActive:!0,createdAt:new Date}),L({name:"",description:"",logoUrl:""}),S(!1),V()}catch(e){console.error("Error creating brand:",e),alert("Error creating brand. Please try again.")}},z=async(e,t)=>{try{await (0,c.mZ)((0,c.H9)(m.db,"brands",e),{isActive:!t}),V()}catch(e){console.error("Error updating brand status:",e)}},J=async(e,t)=>{try{await (0,c.mZ)((0,c.H9)(m.db,"templates",e),{isActive:!t}),V()}catch(e){console.error("Error updating template status:",e)}},F=e=>{E(e),U(!0)},H=async e=>{if(P)try{await (0,c.mZ)((0,c.H9)(m.db,"users",P.id),{...e,updatedAt:new Date}),U(!1),E(null),V(),alert("User updated successfully!")}catch(e){console.error("Error updating user:",e),alert("Error updating user. Please try again.")}},Z=async(e,t)=>{try{await (0,c.mZ)((0,c.H9)(m.db,"users",e),{isActive:!t,updatedAt:new Date}),V()}catch(e){console.error("Error updating user status:",e),alert("Error updating user status. Please try again.")}},W=async e=>{try{let t=(0,x.xI)();await (0,x.J1)(t,e),alert("Password reset email sent to ".concat(e))}catch(e){console.error("Error sending password reset email:",e),alert("Error sending password reset email. Please try again.")}},q=y.filter(e=>{let t=e.email.toLowerCase().includes(B.toLowerCase())||e.firstName&&e.firstName.toLowerCase().includes(B.toLowerCase())||e.lastName&&e.lastName.toLowerCase().includes(B.toLowerCase()),a="all"===T||e.role===T,s="all"===_||"active"===_&&e.isActive||"inactive"===_&&!e.isActive;return t&&a&&s});return t||N?(0,s.jsx)("div",{className:"flex min-h-screen items-center justify-center bg-gray-50 dark:bg-gray-900",children:(0,s.jsx)("div",{className:"text-xl text-gray-900 dark:text-white",children:"Loading..."})}):e&&a?(0,s.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[(0,s.jsx)(o.A,{title:"VALTICS AI - Admin"}),(0,s.jsx)("div",{className:"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"px-4 py-6 sm:px-0",children:[(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"Admin Dashboard"}),(0,s.jsx)("p",{className:"mt-2 text-gray-600 dark:text-gray-300",children:"Manage brands, templates, users, and system settings."})]}),(0,s.jsx)("div",{className:"mb-8",children:(0,s.jsx)("nav",{className:"flex space-x-8",children:[{id:"overview",name:"Overview"},{id:"brands",name:"Brands"},{id:"templates",name:"Templates"},{id:"users",name:"Users"},{id:"settings",name:"Settings"}].map(e=>(0,s.jsx)("button",{onClick:()=>A(e.id),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat(k===e.id?"border-blue-500 text-blue-600 dark:text-blue-400":"border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600"),children:e.name},e.id))})}),"overview"===k&&(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,s.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"flex-shrink-0 w-8 h-8 bg-blue-500 dark:bg-blue-600 rounded-md flex items-center justify-center",children:(0,s.jsx)("span",{className:"text-white text-sm font-bold",children:"\uD83D\uDC65"})}),(0,s.jsxs)("div",{className:"ml-4",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Total Users"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:y.length})]})]})}),(0,s.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"flex-shrink-0 w-8 h-8 bg-green-500 dark:bg-green-600 rounded-md flex items-center justify-center",children:(0,s.jsx)("span",{className:"text-white text-sm font-bold",children:"\uD83C\uDFE2"})}),(0,s.jsxs)("div",{className:"ml-4",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Active Brands"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:g.filter(e=>e.isActive).length})]})]})}),(0,s.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"flex-shrink-0 w-8 h-8 bg-purple-500 dark:bg-purple-600 rounded-md flex items-center justify-center",children:(0,s.jsx)("span",{className:"text-white text-sm font-bold",children:"\uD83D\uDCC4"})}),(0,s.jsxs)("div",{className:"ml-4",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Templates"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:p.length})]})]})}),(0,s.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"flex-shrink-0 w-8 h-8 bg-orange-500 dark:bg-orange-600 rounded-md flex items-center justify-center",children:(0,s.jsx)("span",{className:"text-white text-sm font-bold",children:"\uD83D\uDCCA"})}),(0,s.jsxs)("div",{className:"ml-4",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"BVAs Created"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:f.length})]})]})})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,s.jsx)("div",{className:"bg-white rounded-lg shadow",children:(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Recent Users"}),(0,s.jsx)("div",{className:"space-y-3",children:y.slice(0,5).map(e=>(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-900",children:e.email}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:new Date(e.createdAt).toLocaleDateString()})]}),(0,s.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat("admin"===e.role?"bg-red-100 text-red-800":"bg-blue-100 text-blue-800"),children:e.role})]},e.id))})]})}),(0,s.jsx)("div",{className:"bg-white rounded-lg shadow",children:(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Recent BVAs"}),(0,s.jsx)("div",{className:"space-y-3",children:f.slice(0,5).map(e=>(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:e.clientName})]}),(0,s.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat("completed"===e.status?"bg-green-100 text-green-800":"in-progress"===e.status?"bg-yellow-100 text-yellow-800":"bg-gray-100 text-gray-800"),children:e.status})]},e.id))})]})})]})]}),"brands"===k&&(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Brands Management"}),(0,s.jsx)("button",{onClick:()=>S(!0),className:"bg-blue-600 text-white px-4 py-2 rounded-md font-medium hover:bg-blue-700",children:"Add New Brand"})]}),C&&(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Create New Brand"}),(0,s.jsxs)("form",{onSubmit:O,className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Brand Name *"}),(0,s.jsx)("input",{type:"text",value:D.name,onChange:e=>L({...D,name:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500",required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Description"}),(0,s.jsx)("textarea",{value:D.description,onChange:e=>L({...D,description:e.target.value}),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Logo URL"}),(0,s.jsx)("input",{type:"url",value:D.logoUrl,onChange:e=>L({...D,logoUrl:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"})]}),(0,s.jsxs)("div",{className:"flex space-x-3",children:[(0,s.jsx)("button",{type:"submit",className:"bg-blue-600 text-white px-4 py-2 rounded-md font-medium hover:bg-blue-700",children:"Create Brand"}),(0,s.jsx)("button",{type:"button",onClick:()=>S(!1),className:"bg-gray-300 text-gray-700 px-4 py-2 rounded-md font-medium hover:bg-gray-400",children:"Cancel"})]})]})]}),(0,s.jsx)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:(0,s.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,s.jsx)("thead",{className:"bg-gray-50",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Brand"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Templates"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Created"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,s.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:g.map(e=>(0,s.jsxs)("tr",{children:[(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsxs)("div",{className:"flex items-center",children:[e.logoUrl&&(0,s.jsx)("img",{className:"h-8 w-8 rounded mr-3",src:e.logoUrl,alt:e.name}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,s.jsx)("div",{className:"text-sm text-gray-500",children:e.description})]})]})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:p.filter(t=>t.brandId===e.id).length}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat(e.isActive?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:e.isActive?"Active":"Inactive"})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(e.createdAt).toLocaleDateString()}),(0,s.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:[(0,s.jsx)("button",{onClick:()=>z(e.id,e.isActive),className:"mr-3 ".concat(e.isActive?"text-red-600 hover:text-red-900":"text-green-600 hover:text-green-900"),children:e.isActive?"Deactivate":"Activate"}),(0,s.jsx)(n(),{href:"/admin/brands/".concat(e.id),className:"text-blue-600 hover:text-blue-900",children:"Edit"})]})]},e.id))})]})})]}),"templates"===k&&(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Templates Management"}),(0,s.jsx)(n(),{href:"/admin/templates/new",className:"bg-blue-600 text-white px-4 py-2 rounded-md font-medium hover:bg-blue-700",children:"Create New Template"})]}),(0,s.jsx)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:(0,s.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,s.jsx)("thead",{className:"bg-gray-50",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Template"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Brand"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Category"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Version"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Progress"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,s.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:p.map(e=>{let t=g.find(t=>t.id===e.brandId),a=e.step1Completed||!1,r=e.step2Completed||!1,d=a&&r?100:50*!!a;return(0,s.jsxs)("tr",{children:[(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,s.jsx)("div",{className:"text-sm text-gray-500",children:e.description})]})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:(null==t?void 0:t.name)||"Unknown"}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.category}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.templateVersion||"1.0"}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"w-16 bg-gray-200 rounded-full h-2 mr-2",children:(0,s.jsx)("div",{className:"h-2 rounded-full ".concat(100===d?"bg-green-500":"bg-blue-500"),style:{width:"".concat(d,"%")}})}),(0,s.jsxs)("span",{className:"text-xs text-gray-500",children:[d,"%"]})]})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsxs)("div",{className:"flex flex-col space-y-1",children:[(0,s.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat("published"===e.status?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800"),children:"published"===e.status?"Published":"Draft"}),(0,s.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat(e.isActive?"bg-blue-100 text-blue-800":"bg-gray-100 text-gray-800"),children:e.isActive?"Active":"Inactive"})]})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,s.jsxs)("div",{className:"flex flex-col space-y-1",children:[(0,s.jsx)(n(),{href:"/admin/templates/".concat(e.id,"/edit"),className:"text-blue-600 hover:text-blue-900",children:"published"===e.status?"View":"Edit"}),"published"===e.status&&(0,s.jsx)(n(),{href:"/admin/templates/".concat(e.id,"/edit"),className:"text-green-600 hover:text-green-900",children:"Create New Version"}),(0,s.jsx)("button",{onClick:()=>J(e.id,e.isActive),className:"text-left ".concat(e.isActive?"text-red-600 hover:text-red-900":"text-green-600 hover:text-green-900"),children:e.isActive?"Deactivate":"Activate"})]})})]},e.id)})})]})})]}),"users"===k&&(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)("div",{className:"flex justify-between items-center",children:(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"User Management"})}),(0,s.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Search Users"}),(0,s.jsx)("input",{type:"text",placeholder:"Search by name or email...",value:B,onChange:e=>M(e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Filter by Role"}),(0,s.jsxs)("select",{value:T,onChange:e=>R(e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500",children:[(0,s.jsx)("option",{value:"all",children:"All Roles"}),(0,s.jsx)("option",{value:"admin",children:"Admin"}),(0,s.jsx)("option",{value:"user",children:"User"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Filter by Status"}),(0,s.jsxs)("select",{value:_,onChange:e=>G(e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500",children:[(0,s.jsx)("option",{value:"all",children:"All Status"}),(0,s.jsx)("option",{value:"active",children:"Active"}),(0,s.jsx)("option",{value:"inactive",children:"Inactive"})]})]})]})}),(0,s.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden",children:[(0,s.jsxs)("table",{className:"min-w-full divide-y divide-gray-200 dark:divide-gray-600",children:[(0,s.jsx)("thead",{className:"bg-gray-50 dark:bg-gray-700",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"User"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Role"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Status"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Last Login"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Created"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Actions"})]})}),(0,s.jsx)("tbody",{className:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-600",children:q.map(e=>(0,s.jsxs)("tr",{children:[(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:e.firstName&&e.lastName?"".concat(e.firstName," ").concat(e.lastName):e.email}),(0,s.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:e.email})]})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat("admin"===e.role?"bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200":"bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200"),children:e.role})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat(e.isActive?"bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200":"bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200"),children:e.isActive?"Active":"Inactive"})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400",children:e.lastLoginAt?new Date(e.lastLoginAt).toLocaleDateString():"Never"}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400",children:new Date(e.createdAt).toLocaleDateString()}),(0,s.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2",children:[(0,s.jsx)("button",{onClick:()=>F(e),className:"text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300",children:"Edit"}),(0,s.jsx)("button",{onClick:()=>Z(e.id,e.isActive),className:"".concat(e.isActive?"text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300":"text-green-600 dark:text-green-400 hover:text-green-900 dark:hover:text-green-300"),children:e.isActive?"Disable":"Enable"}),(0,s.jsx)("button",{onClick:()=>W(e.email),className:"text-orange-600 dark:text-orange-400 hover:text-orange-900 dark:hover:text-orange-300",children:"Reset Password"})]})]},e.id))})]}),0===q.length&&(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)("div",{className:"text-gray-400 dark:text-gray-500 text-4xl mb-4",children:"\uD83D\uDC65"}),(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:"No users found"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-300",children:"Try adjusting your search or filter criteria."})]})]})]}),"settings"===k&&(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)("div",{className:"flex justify-between items-center",children:(0,s.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:"System Settings"})}),(0,s.jsx)("div",{className:"bg-white dark:bg-gray-800 shadow rounded-lg p-6",children:(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-4",children:"AI API Configuration"}),(0,s.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-4",children:"Configure API keys for Claude AI and OpenAI to enable AI artifact generation in templates."}),(0,s.jsxs)(n(),{href:"/admin/settings",className:"bg-blue-600 dark:bg-blue-700 text-white px-4 py-2 rounded-md font-medium hover:bg-blue-700 dark:hover:bg-blue-800 inline-flex items-center",children:[(0,s.jsxs)("svg",{className:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"}),(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})]}),"Configure API Keys"]})]}),(0,s.jsxs)("div",{className:"border-t border-gray-200 dark:border-gray-700 pt-6",children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-4",children:"System Information"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg",children:[(0,s.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:"Total Brands"}),(0,s.jsx)("div",{className:"text-2xl font-bold text-blue-600 dark:text-blue-400",children:g.length})]}),(0,s.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg",children:[(0,s.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:"Total Templates"}),(0,s.jsx)("div",{className:"text-2xl font-bold text-green-600 dark:text-green-400",children:p.length})]}),(0,s.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg",children:[(0,s.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:"Total Users"}),(0,s.jsx)("div",{className:"text-2xl font-bold text-purple-600 dark:text-purple-400",children:y.length})]}),(0,s.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg",children:[(0,s.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:"BVA Instances"}),(0,s.jsx)("div",{className:"text-2xl font-bold text-orange-600 dark:text-orange-400",children:f.length})]})]})]})]})})]})]})}),I&&P&&(0,s.jsx)(h,{user:P,onSave:H,onClose:()=>{U(!1),E(null)}})]}):null}function h(e){let{user:t,onSave:a,onClose:d}=e,[l,i]=(0,r.useState)({firstName:t.firstName||"",lastName:t.lastName||"",email:t.email,role:t.role,isActive:t.isActive});return(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:(0,s.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full",children:(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white",children:"Edit User"}),(0,s.jsxs)("button",{onClick:d,className:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",children:[(0,s.jsx)("span",{className:"sr-only",children:"Close"}),(0,s.jsx)("svg",{className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})]})]}),(0,s.jsxs)("form",{onSubmit:e=>{e.preventDefault(),a(l)},className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"First Name"}),(0,s.jsx)("input",{type:"text",value:l.firstName,onChange:e=>i({...l,firstName:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter first name"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Last Name"}),(0,s.jsx)("input",{type:"text",value:l.lastName,onChange:e=>i({...l,lastName:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter last name"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Email"}),(0,s.jsx)("input",{type:"email",value:l.email,onChange:e=>i({...l,email:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500",required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Role"}),(0,s.jsxs)("select",{value:l.role,onChange:e=>i({...l,role:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500",children:[(0,s.jsx)("option",{value:"user",children:"User"}),(0,s.jsx)("option",{value:"admin",children:"Admin"})]})]}),(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("input",{type:"checkbox",id:"isActive",checked:l.isActive,onChange:e=>i({...l,isActive:e.target.checked}),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded"}),(0,s.jsx)("label",{htmlFor:"isActive",className:"ml-2 block text-sm text-gray-700 dark:text-gray-300",children:"Active User"})]}),(0,s.jsxs)("div",{className:"flex space-x-3 pt-4",children:[(0,s.jsx)("button",{type:"submit",className:"flex-1 bg-blue-600 dark:bg-blue-700 text-white px-4 py-2 rounded-md font-medium hover:bg-blue-700 dark:hover:bg-blue-800",children:"Save Changes"}),(0,s.jsx)("button",{type:"button",onClick:d,className:"flex-1 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 px-4 py-2 rounded-md font-medium hover:bg-gray-400 dark:hover:bg-gray-500",children:"Cancel"})]})]})]})})})}},7603:(e,t,a)=>{Promise.resolve().then(a.bind(a,7161))}},e=>{var t=t=>e(e.s=t);e.O(0,[992,965,288,874,63,573,441,684,358],()=>t(7603)),_N_E=e.O()}]);