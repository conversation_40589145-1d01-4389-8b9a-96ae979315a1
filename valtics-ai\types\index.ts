export interface User {
  id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  role: 'admin' | 'user';
  isActive: boolean;
  lastLoginAt?: Date;
  createdAt: Date;
  updatedAt?: Date;
  subscription?: Subscription;
  // Trial system fields
  isTrialUser: boolean;
  trialStartDate?: Date;
  trialEndDate?: Date;
  trialExpired: boolean;
}

export interface Brand {
  id: string;
  name: string;
  description: string;
  logoUrl?: string;
  isActive: boolean;
  createdAt: Date;
}

export interface Template {
  id: string;
  name: string;
  description: string;
  brandId: string;
  category: string;
  fileUrls: {
    pdf?: string;
    ppt?: string;
    excel?: string;
  };
  price: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  // New fields for enhanced template creation
  solutionProviderName: string;
  solutionName: string;
  solutionDescription: string;
  templateVersion: string;
  templateVersionDate: Date;
  status: 'draft' | 'published';
  // Step 2 documents
  enterpriseNeed?: string;
  solutionDescriptionDocument?: string;
  riskOfNoInvestment?: string;
  // Document uploads
  enterpriseNeedFileUrl?: string;
  solutionDescriptionFileUrl?: string;
  riskOfNoInvestmentFileUrl?: string;
  // Completion tracking
  step1Completed: boolean;
  step2Completed: boolean;
  // Publishing and versioning
  publishedAt?: Date;
  basedOnTemplateId?: string;
  basedOnVersion?: string;
}

export interface BVAInstance {
  id: string;
  userId: string;
  templateId: string;
  name: string;
  clientName: string;
  status: 'draft' | 'in-progress' | 'completed';
  data: Record<string, any>;
  executiveSummaryUrl?: string;
  fullReportUrl?: string;
  createdAt: Date;
  updatedAt: Date;
  completedAt?: Date;
}

export interface Subscription {
  id: string;
  userId: string;
  status: 'active' | 'inactive' | 'cancelled';
  type: 'basic' | 'premium' | 'enterprise';
  startDate: Date;
  endDate: Date;
  accessibleTemplateIds: string[];
}

// Additional types for BVA Builder
export interface BVAFormData {
  clientName: string;
  projectName: string;
  industry: string;
  currentCosts: number;
  expectedBenefits: number;
  timeframe: number;
  additionalData: Record<string, any>;
}

export interface BVAReport {
  executiveSummary: string;
  roi: number;
  paybackPeriod: number;
  totalBenefits: number;
  totalCosts: number;
  charts: ChartData[];
}

export interface ChartData {
  type: 'bar' | 'line' | 'pie';
  title: string;
  data: any[];
  labels: string[];
}