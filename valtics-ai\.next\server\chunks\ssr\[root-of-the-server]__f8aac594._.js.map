{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/source/repos/ravihani/valtics/valtics-ai/app/templates/%5Bid%5D/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { useRouter, useParams } from 'next/navigation';\nimport Link from 'next/link';\nimport { doc, getDoc } from 'firebase/firestore';\nimport { db } from '@/lib/firebase/config';\nimport { Template, Brand } from '@/types';\n\nexport default function TemplateDetail() {\n  const { user, loading } = useAuth();\n  const router = useRouter();\n  const params = useParams();\n  const templateId = params.id as string;\n  \n  const [template, setTemplate] = useState<Template | null>(null);\n  const [brand, setBrand] = useState<Brand | null>(null);\n  const [loadingData, setLoadingData] = useState(true);\n\n  useEffect(() => {\n    if (!loading && !user) {\n      router.push('/login');\n    }\n  }, [user, loading, router]);\n\n  useEffect(() => {\n    if (user && templateId) {\n      fetchTemplateData();\n    }\n  }, [user, templateId]);\n\n  const fetchTemplateData = async () => {\n    try {\n      setLoadingData(true);\n\n      // Fetch template\n      const templateDoc = await getDoc(doc(db, 'templates', templateId));\n      if (templateDoc.exists()) {\n        const templateData = { id: templateDoc.id, ...templateDoc.data() } as Template;\n        setTemplate(templateData);\n\n        // Fetch brand\n        const brandDoc = await getDoc(doc(db, 'brands', templateData.brandId));\n        if (brandDoc.exists()) {\n          setBrand({ id: brandDoc.id, ...brandDoc.data() } as Brand);\n        }\n      } else {\n        router.push('/templates');\n      }\n\n    } catch (error) {\n      console.error('Error fetching template data:', error);\n      router.push('/templates');\n    } finally {\n      setLoadingData(false);\n    }\n  };\n\n  if (loading || loadingData) {\n    return (\n      <div className=\"flex min-h-screen items-center justify-center\">\n        <div className=\"text-xl\">Loading...</div>\n      </div>\n    );\n  }\n\n  if (!user || !template) {\n    return null;\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Navigation */}\n      <nav className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between h-16\">\n            <div className=\"flex items-center\">\n              <Link href=\"/dashboard\" className=\"text-xl font-semibold text-gray-900\">\n                VALTICS AI\n              </Link>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <Link\n                href=\"/templates\"\n                className=\"text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium\"\n              >\n                ← Back to Templates\n              </Link>\n            </div>\n          </div>\n        </div>\n      </nav>\n\n      {/* Main Content */}\n      <div className=\"max-w-4xl mx-auto py-6 sm:px-6 lg:px-8\">\n        <div className=\"px-4 py-6 sm:px-0\">\n          {/* Template Header */}\n          <div className=\"bg-white rounded-lg shadow-md overflow-hidden mb-8\">\n            <div className=\"p-8\">\n              {/* Brand info */}\n              {brand && (\n                <div className=\"flex items-center mb-6\">\n                  {brand.logoUrl && (\n                    <img\n                      src={brand.logoUrl}\n                      alt={brand.name}\n                      className=\"h-8 w-auto mr-3\"\n                    />\n                  )}\n                  <div>\n                    <h2 className=\"text-lg font-medium text-gray-900\">{brand.name}</h2>\n                    <p className=\"text-sm text-gray-500\">{brand.description}</p>\n                  </div>\n                </div>\n              )}\n\n              <div className=\"flex items-start justify-between\">\n                <div className=\"flex-1\">\n                  <h1 className=\"text-3xl font-bold text-gray-900 mb-4\">\n                    {template.name}\n                  </h1>\n                  \n                  <p className=\"text-lg text-gray-600 mb-6\">\n                    {template.description}\n                  </p>\n\n                  <div className=\"flex items-center space-x-4 mb-6\">\n                    <span className=\"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800\">\n                      {template.category}\n                    </span>\n                    <span className=\"text-2xl font-bold text-green-600\">\n                      ${template.price}\n                    </span>\n                  </div>\n                </div>\n              </div>\n\n              {/* Available Formats */}\n              <div className=\"mb-8\">\n                <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Available Formats</h3>\n                <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                  {template.fileUrls.pdf && (\n                    <div className=\"border border-gray-200 rounded-lg p-4\">\n                      <div className=\"flex items-center mb-2\">\n                        <div className=\"w-8 h-8 bg-red-500 rounded flex items-center justify-center mr-3\">\n                          <span className=\"text-white text-xs font-bold\">PDF</span>\n                        </div>\n                        <span className=\"font-medium\">PDF Document</span>\n                      </div>\n                      <p className=\"text-sm text-gray-600 mb-3\">\n                        Professional presentation format, perfect for client meetings and executive reviews.\n                      </p>\n                      <a\n                        href={template.fileUrls.pdf}\n                        target=\"_blank\"\n                        rel=\"noopener noreferrer\"\n                        className=\"text-blue-600 hover:text-blue-800 text-sm font-medium\"\n                      >\n                        Preview PDF →\n                      </a>\n                    </div>\n                  )}\n\n                  {template.fileUrls.ppt && (\n                    <div className=\"border border-gray-200 rounded-lg p-4\">\n                      <div className=\"flex items-center mb-2\">\n                        <div className=\"w-8 h-8 bg-orange-500 rounded flex items-center justify-center mr-3\">\n                          <span className=\"text-white text-xs font-bold\">PPT</span>\n                        </div>\n                        <span className=\"font-medium\">PowerPoint</span>\n                      </div>\n                      <p className=\"text-sm text-gray-600 mb-3\">\n                        Editable presentation slides for customization and interactive presentations.\n                      </p>\n                      <a\n                        href={template.fileUrls.ppt}\n                        target=\"_blank\"\n                        rel=\"noopener noreferrer\"\n                        className=\"text-blue-600 hover:text-blue-800 text-sm font-medium\"\n                      >\n                        Download PPT →\n                      </a>\n                    </div>\n                  )}\n\n                  {template.fileUrls.excel && (\n                    <div className=\"border border-gray-200 rounded-lg p-4\">\n                      <div className=\"flex items-center mb-2\">\n                        <div className=\"w-8 h-8 bg-green-500 rounded flex items-center justify-center mr-3\">\n                          <span className=\"text-white text-xs font-bold\">XLS</span>\n                        </div>\n                        <span className=\"font-medium\">Excel Workbook</span>\n                      </div>\n                      <p className=\"text-sm text-gray-600 mb-3\">\n                        Detailed calculations and data analysis with formulas and charts.\n                      </p>\n                      <a\n                        href={template.fileUrls.excel}\n                        target=\"_blank\"\n                        rel=\"noopener noreferrer\"\n                        className=\"text-blue-600 hover:text-blue-800 text-sm font-medium\"\n                      >\n                        Download Excel →\n                      </a>\n                    </div>\n                  )}\n                </div>\n              </div>\n\n              {/* Template Features */}\n              <div className=\"mb-8\">\n                <h3 className=\"text-lg font-medium text-gray-900 mb-4\">What's Included</h3>\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  <div className=\"flex items-start\">\n                    <div className=\"flex-shrink-0 w-5 h-5 bg-green-500 rounded-full flex items-center justify-center mr-3 mt-0.5\">\n                      <span className=\"text-white text-xs\">✓</span>\n                    </div>\n                    <div>\n                      <h4 className=\"font-medium text-gray-900\">Executive Summary Template</h4>\n                      <p className=\"text-sm text-gray-600\">Pre-formatted executive summary with key metrics</p>\n                    </div>\n                  </div>\n                  \n                  <div className=\"flex items-start\">\n                    <div className=\"flex-shrink-0 w-5 h-5 bg-green-500 rounded-full flex items-center justify-center mr-3 mt-0.5\">\n                      <span className=\"text-white text-xs\">✓</span>\n                    </div>\n                    <div>\n                      <h4 className=\"font-medium text-gray-900\">ROI Calculations</h4>\n                      <p className=\"text-sm text-gray-600\">Automated ROI and payback period calculations</p>\n                    </div>\n                  </div>\n                  \n                  <div className=\"flex items-start\">\n                    <div className=\"flex-shrink-0 w-5 h-5 bg-green-500 rounded-full flex items-center justify-center mr-3 mt-0.5\">\n                      <span className=\"text-white text-xs\">✓</span>\n                    </div>\n                    <div>\n                      <h4 className=\"font-medium text-gray-900\">Visual Charts & Graphs</h4>\n                      <p className=\"text-sm text-gray-600\">Professional charts for data visualization</p>\n                    </div>\n                  </div>\n                  \n                  <div className=\"flex items-start\">\n                    <div className=\"flex-shrink-0 w-5 h-5 bg-green-500 rounded-full flex items-center justify-center mr-3 mt-0.5\">\n                      <span className=\"text-white text-xs\">✓</span>\n                    </div>\n                    <div>\n                      <h4 className=\"font-medium text-gray-900\">Customizable Sections</h4>\n                      <p className=\"text-sm text-gray-600\">Flexible sections for your specific use case</p>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              {/* Action Buttons */}\n              <div className=\"flex space-x-4\">\n                <Link\n                  href={`/bva/new?template=${template.id}`}\n                  className=\"bg-blue-600 text-white px-8 py-3 rounded-md text-lg font-medium hover:bg-blue-700 transition-colors\"\n                >\n                  Start BVA with this Template\n                </Link>\n                <Link\n                  href=\"/templates\"\n                  className=\"bg-gray-200 text-gray-800 px-8 py-3 rounded-md text-lg font-medium hover:bg-gray-300 transition-colors\"\n                >\n                  Browse Other Templates\n                </Link>\n              </div>\n            </div>\n          </div>\n\n          {/* Additional Information */}\n          <div className=\"bg-white rounded-lg shadow-md overflow-hidden\">\n            <div className=\"p-6\">\n              <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Template Information</h3>\n              <dl className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div>\n                  <dt className=\"text-sm font-medium text-gray-500\">Created</dt>\n                  <dd className=\"text-sm text-gray-900\">\n                    {new Date(template.createdAt).toLocaleDateString()}\n                  </dd>\n                </div>\n                <div>\n                  <dt className=\"text-sm font-medium text-gray-500\">Last Updated</dt>\n                  <dd className=\"text-sm text-gray-900\">\n                    {new Date(template.updatedAt).toLocaleDateString()}\n                  </dd>\n                </div>\n                <div>\n                  <dt className=\"text-sm font-medium text-gray-500\">Category</dt>\n                  <dd className=\"text-sm text-gray-900\">{template.category}</dd>\n                </div>\n                <div>\n                  <dt className=\"text-sm font-medium text-gray-500\">Brand</dt>\n                  <dd className=\"text-sm text-gray-900\">{brand?.name}</dd>\n                </div>\n              </dl>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AACA;AAPA;;;;;;;;AAUe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,UAAO,AAAD;IAChC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,aAAa,OAAO,EAAE;IAE5B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IAC1D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW,CAAC,MAAM;YACrB,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAM;QAAS;KAAO;IAE1B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ,YAAY;YACtB;QACF;IACF,GAAG;QAAC;QAAM;KAAW;IAErB,MAAM,oBAAoB;QACxB,IAAI;YACF,eAAe;YAEf,iBAAiB;YACjB,MAAM,cAAc,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,aAAa;YACtD,IAAI,YAAY,MAAM,IAAI;gBACxB,MAAM,eAAe;oBAAE,IAAI,YAAY,EAAE;oBAAE,GAAG,YAAY,IAAI,EAAE;gBAAC;gBACjE,YAAY;gBAEZ,cAAc;gBACd,MAAM,WAAW,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,UAAU,aAAa,OAAO;gBACpE,IAAI,SAAS,MAAM,IAAI;oBACrB,SAAS;wBAAE,IAAI,SAAS,EAAE;wBAAE,GAAG,SAAS,IAAI,EAAE;oBAAC;gBACjD;YACF,OAAO;gBACL,OAAO,IAAI,CAAC;YACd;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,OAAO,IAAI,CAAC;QACd,SAAU;YACR,eAAe;QACjB;IACF;IAEA,IAAI,WAAW,aAAa;QAC1B,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BAAU;;;;;;;;;;;IAG/B;IAEA,IAAI,CAAC,QAAQ,CAAC,UAAU;QACtB,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAa,WAAU;8CAAsC;;;;;;;;;;;0CAI1E,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAST,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;oCAEZ,uBACC,8OAAC;wCAAI,WAAU;;4CACZ,MAAM,OAAO,kBACZ,8OAAC;gDACC,KAAK,MAAM,OAAO;gDAClB,KAAK,MAAM,IAAI;gDACf,WAAU;;;;;;0DAGd,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAqC,MAAM,IAAI;;;;;;kEAC7D,8OAAC;wDAAE,WAAU;kEAAyB,MAAM,WAAW;;;;;;;;;;;;;;;;;;kDAK7D,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DACX,SAAS,IAAI;;;;;;8DAGhB,8OAAC;oDAAE,WAAU;8DACV,SAAS,WAAW;;;;;;8DAGvB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEACb,SAAS,QAAQ;;;;;;sEAEpB,8OAAC;4DAAK,WAAU;;gEAAoC;gEAChD,SAAS,KAAK;;;;;;;;;;;;;;;;;;;;;;;;kDAOxB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAyC;;;;;;0DACvD,8OAAC;gDAAI,WAAU;;oDACZ,SAAS,QAAQ,CAAC,GAAG,kBACpB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC;4EAAK,WAAU;sFAA+B;;;;;;;;;;;kFAEjD,8OAAC;wEAAK,WAAU;kFAAc;;;;;;;;;;;;0EAEhC,8OAAC;gEAAE,WAAU;0EAA6B;;;;;;0EAG1C,8OAAC;gEACC,MAAM,SAAS,QAAQ,CAAC,GAAG;gEAC3B,QAAO;gEACP,KAAI;gEACJ,WAAU;0EACX;;;;;;;;;;;;oDAMJ,SAAS,QAAQ,CAAC,GAAG,kBACpB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC;4EAAK,WAAU;sFAA+B;;;;;;;;;;;kFAEjD,8OAAC;wEAAK,WAAU;kFAAc;;;;;;;;;;;;0EAEhC,8OAAC;gEAAE,WAAU;0EAA6B;;;;;;0EAG1C,8OAAC;gEACC,MAAM,SAAS,QAAQ,CAAC,GAAG;gEAC3B,QAAO;gEACP,KAAI;gEACJ,WAAU;0EACX;;;;;;;;;;;;oDAMJ,SAAS,QAAQ,CAAC,KAAK,kBACtB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC;4EAAK,WAAU;sFAA+B;;;;;;;;;;;kFAEjD,8OAAC;wEAAK,WAAU;kFAAc;;;;;;;;;;;;0EAEhC,8OAAC;gEAAE,WAAU;0EAA6B;;;;;;0EAG1C,8OAAC;gEACC,MAAM,SAAS,QAAQ,CAAC,KAAK;gEAC7B,QAAO;gEACP,KAAI;gEACJ,WAAU;0EACX;;;;;;;;;;;;;;;;;;;;;;;;kDAST,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAyC;;;;;;0DACvD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAK,WAAU;8EAAqB;;;;;;;;;;;0EAEvC,8OAAC;;kFACC,8OAAC;wEAAG,WAAU;kFAA4B;;;;;;kFAC1C,8OAAC;wEAAE,WAAU;kFAAwB;;;;;;;;;;;;;;;;;;kEAIzC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAK,WAAU;8EAAqB;;;;;;;;;;;0EAEvC,8OAAC;;kFACC,8OAAC;wEAAG,WAAU;kFAA4B;;;;;;kFAC1C,8OAAC;wEAAE,WAAU;kFAAwB;;;;;;;;;;;;;;;;;;kEAIzC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAK,WAAU;8EAAqB;;;;;;;;;;;0EAEvC,8OAAC;;kFACC,8OAAC;wEAAG,WAAU;kFAA4B;;;;;;kFAC1C,8OAAC;wEAAE,WAAU;kFAAwB;;;;;;;;;;;;;;;;;;kEAIzC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAK,WAAU;8EAAqB;;;;;;;;;;;0EAEvC,8OAAC;;kFACC,8OAAC;wEAAG,WAAU;kFAA4B;;;;;;kFAC1C,8OAAC;wEAAE,WAAU;kFAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAO7C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,CAAC,kBAAkB,EAAE,SAAS,EAAE,EAAE;gDACxC,WAAU;0DACX;;;;;;0DAGD,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;sCAQP,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAyC;;;;;;kDACvD,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAoC;;;;;;kEAClD,8OAAC;wDAAG,WAAU;kEACX,IAAI,KAAK,SAAS,SAAS,EAAE,kBAAkB;;;;;;;;;;;;0DAGpD,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAoC;;;;;;kEAClD,8OAAC;wDAAG,WAAU;kEACX,IAAI,KAAK,SAAS,SAAS,EAAE,kBAAkB;;;;;;;;;;;;0DAGpD,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAoC;;;;;;kEAClD,8OAAC;wDAAG,WAAU;kEAAyB,SAAS,QAAQ;;;;;;;;;;;;0DAE1D,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAoC;;;;;;kEAClD,8OAAC;wDAAG,WAAU;kEAAyB,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAShE", "debugId": null}}]}