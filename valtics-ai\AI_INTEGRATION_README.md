# VALTICS AI - AI Integration Implementation

This document describes the complete AI integration implementation for the VALTICS AI template creation workflow.

## 🚀 Features Implemented

### ✅ Real AI API Integration
- **Claude AI (Anthropic)** integration with retry logic and error handling
- **OpenAI GPT-4** integration with proper prompt engineering
- Automatic API key validation and connection testing
- Rate limiting and timeout handling

### ✅ Document Processing
- **PDF extraction** using pdf-parse library
- **Word document extraction** (.docx, .doc) using mammoth library
- **Text file processing** with proper encoding handling
- File validation (type and size limits)
- Content cleaning and preparation for AI processing

### ✅ Admin API Key Management
- Secure API key storage with AES encryption
- Admin settings page at `/admin/settings`
- Test connection functionality for both APIs
- Real-time status indicators (Connected/Error/Untested)
- Validation for API key formats

### ✅ Enhanced Storage System
- AI artifacts saved as individual markdown files in Firebase Storage
- Proper file naming convention: `templates/{templateId}/artifacts/{type}-{timestamp}.md`
- File URLs stored in template document alongside text content
- Automatic cleanup of old artifacts

### ✅ 3-Step Template Workflow
1. **Step 1**: Basic template information
2. **Step 2**: Document uploads (removed rich text editors)
3. **Step 3**: AI artifact generation with real-time processing

## 📁 File Structure

```
valtics-ai/
├── lib/
│   ├── services/
│   │   ├── aiService.ts          # AI API integration (Claude & OpenAI)
│   │   └── apiConfigService.ts   # API key management & encryption
│   └── utils/
│       └── fileProcessor.ts      # Document content extraction
├── app/
│   ├── admin/
│   │   └── settings/
│   │       └── page.tsx          # API key configuration UI
│   └── api/
│       └── templates/
│           └── [id]/
│               └── generate-artifacts/
│                   └── route.ts  # AI generation API endpoint
└── types/
    └── index.ts                  # Enhanced type definitions
```

## 🔧 Setup Instructions

### 1. Install Dependencies
```bash
npm install @anthropic-ai/sdk openai pdf-parse mammoth crypto-js --legacy-peer-deps
```

### 2. Environment Variables
Copy `.env.example` to `.env.local` and configure:
```env
NEXT_PUBLIC_ENCRYPTION_KEY=your_secure_encryption_key_here
```

### 3. Firebase Security Rules
Update your Firestore security rules using the rules in `firestore-security-rules-update.md`

### 4. API Key Configuration
1. Go to `/admin/settings` as an admin user
2. Enter your Claude AI and/or OpenAI API keys
3. Test the connections to verify they work
4. API keys are automatically encrypted before storage

## 🔐 Security Features

- **Encrypted API Key Storage**: All API keys are encrypted using AES encryption before storing in Firestore
- **Admin-Only Access**: Only admin users can configure and view API keys
- **Secure Firestore Rules**: API configuration collection is protected by admin-only rules
- **Client-Side Protection**: API keys are never exposed in client-side code
- **Input Validation**: All file uploads and API inputs are validated

## 🎯 Usage Workflow

### For Admins:
1. **Configure API Keys**: Go to Admin Settings and add Claude/OpenAI API keys
2. **Test Connections**: Use the test button to verify API connectivity
3. **Create Templates**: Follow the 3-step template creation process

### For Template Creation:
1. **Step 1**: Enter basic template information
2. **Step 2**: Upload three required documents (PDF, DOCX, or TXT)
   - Enterprise Need document
   - Solution Description document  
   - Risk of No Investment document
3. **Step 3**: Generate AI artifacts
   - Select AI model (Claude or OpenAI)
   - Enter company name (optional)
   - Review the generated prompt
   - Click "Generate AI Artifacts"
   - Wait for processing (real-time status updates)
   - Review generated artifacts
   - Publish template when satisfied

## 📊 AI Artifact Generation Process

1. **Validation**: Check API keys are configured and files are uploaded
2. **Document Processing**: Extract text content from uploaded files
3. **Content Preparation**: Clean and format text for AI processing
4. **AI Generation**: Send prompt with document context to selected AI model
5. **Storage**: Save generated artifacts as markdown files in Firebase Storage
6. **Database Update**: Store both text content and file URLs in template document
7. **Status Update**: Mark Step 3 as completed and template ready for publishing

## 🔄 Error Handling & Retry Logic

- **API Failures**: Automatic retry with exponential backoff (up to 3 attempts)
- **File Processing Errors**: Detailed error messages for unsupported formats
- **Network Issues**: Timeout handling and connection error recovery
- **User Feedback**: Clear error messages with actionable next steps
- **Graceful Degradation**: System remains functional even if AI services are unavailable

## 📝 Supported File Formats

- **PDF**: Full text extraction using pdf-parse
- **Word Documents**: .docx and .doc files using mammoth
- **Text Files**: .txt files with UTF-8 encoding
- **File Size Limit**: 10MB maximum per file
- **Validation**: Automatic file type and size validation

## 🚨 Important Notes

1. **API Costs**: Both Claude and OpenAI APIs have usage costs - monitor your usage
2. **Rate Limits**: APIs have rate limits - the system includes retry logic to handle this
3. **File Security**: Uploaded documents are stored in Firebase Storage with proper access controls
4. **Backup**: Generated artifacts are stored both as text in Firestore and as files in Storage
5. **Performance**: Large documents may take longer to process - users see real-time status updates

## 🔧 Troubleshooting

### API Key Issues
- Verify API keys are correctly formatted (Claude: `sk-ant-...`, OpenAI: `sk-...`)
- Test connections in Admin Settings
- Check API key permissions and billing status

### File Processing Issues
- Ensure files are in supported formats (PDF, DOCX, DOC, TXT)
- Check file size is under 10MB limit
- Verify files are not corrupted or password-protected

### Generation Failures
- Check browser console for detailed error logs
- Verify all three documents are uploaded
- Ensure API keys have sufficient credits/quota
- Try with a different AI model if one fails

## 📈 Future Enhancements

- Support for additional file formats (PPT, Excel, etc.)
- Custom prompt templates for different use cases
- Batch processing for multiple templates
- AI model comparison and A/B testing
- Advanced analytics on generation success rates
- Integration with additional AI providers
