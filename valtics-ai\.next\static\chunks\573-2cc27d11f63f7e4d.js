"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[573],{1138:(e,t,r)=>{r.d(t,{IG:()=>c,db:()=>o,j2:()=>n});var a=r(3915),l=r(6203),s=r(5317),i=r(858);let d=(0,a.Dk)().length?(0,a.Sx)():(0,a.Wp)({apiKey:"AIzaSyDk_NWWIicxaEXYSWIoM-24-d9QrnrMhrg",authDomain:"ai-app-d5ee6.firebaseapp.com",projectId:"ai-app-d5ee6",storageBucket:"ai-app-d5ee6.firebasestorage.app",messagingSenderId:"81717928007",appId:"1:81717928007:web:c21e9ccb3abae564f29162"}),n=(0,l.xI)(d),o=(0,s.aU)(d),c=(0,i.c7)(d)},1573:(e,t,r)=>{r.d(t,{A:()=>u});var a=r(5155),l=r(6874),s=r.n(l),i=r(6766),d=r(3274),n=r(5695),o=r(3074),c=r(8679);function u(e){let{title:t="VALTICS AI",showBackButton:r=!1,backUrl:l="/dashboard",backText:u="← Back to Dashboard"}=e,{user:x,logOut:h,isAdmin:m}=(0,d.A)(),g=(0,n.useRouter)(),w=async()=>{try{await h(),g.push("/")}catch(e){console.error("Error logging out:",e)}};return x?(0,a.jsx)("nav",{className:"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex justify-between h-16",children:[(0,a.jsx)("div",{className:"flex items-center",children:(0,a.jsxs)(s(),{href:"/dashboard",className:"flex items-center space-x-3",children:[(0,a.jsx)(i.default,{src:"/logo.png",alt:"VALTICS AI Logo",width:32,height:32,className:"w-8 h-8"}),(0,a.jsx)("span",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:t})]})}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[r&&(0,a.jsx)(s(),{href:l,className:"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium",children:u}),!r&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(s(),{href:"/brands",className:"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium",children:"Brands"}),(0,a.jsx)(s(),{href:"/templates",className:"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium",children:"Templates"}),m&&(0,a.jsx)(s(),{href:"/admin",className:"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium",children:"Admin"}),(0,a.jsx)(s(),{href:"/profile",className:"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium",children:"Profile"})]}),(0,a.jsx)(c.I,{}),(0,a.jsx)(o.default,{}),(0,a.jsx)("button",{onClick:w,className:"bg-red-600 dark:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-red-700 dark:hover:bg-red-800",children:"Logout"})]})]})})}):null}},3074:(e,t,r)=>{r.d(t,{default:()=>i});var a=r(5155),l=r(3391),s=r(2115);function i(){let[e,t]=(0,s.useState)(!1),[r,i]=(0,s.useState)("light"),d=(0,l.Q)();(0,s.useEffect)(()=>{if(t(!0),!d){let e=localStorage.getItem("theme");if(e)i(e),n(e);else{let e=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light";i(e),n(e)}}},[d]);let n=e=>{let t=document.documentElement;"dark"===e?t.classList.add("dark"):t.classList.remove("dark")};if(!e)return(0,a.jsx)("div",{className:"p-2 w-9 h-9"});let o=d?d.theme:r;return(0,a.jsx)("button",{onClick:()=>{if(d)d.toggleTheme();else{let e="light"===r?"dark":"light";i(e),n(e),localStorage.setItem("theme",e)}},className:"p-2 rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-100 dark:text-gray-300 dark:hover:text-gray-100 dark:hover:bg-gray-700 transition-colors","aria-label":"Switch to ".concat("light"===o?"dark":"light"," mode"),title:"Switch to ".concat("light"===o?"dark":"light"," mode"),children:"light"===o?(0,a.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"})}):(0,a.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"})})})}},3274:(e,t,r)=>{r.d(t,{A:()=>u,AuthProvider:()=>c});var a=r(5155),l=r(2115),s=r(6203),i=r(1138),d=r(5317),n=r(9886);let o=(0,l.createContext)(null),c=e=>{let{children:t}=e,[r,c]=(0,l.useState)(null),[u,x]=(0,l.useState)(null),[h,m]=(0,l.useState)(!0),[g,w]=(0,l.useState)(!1),[f,p]=(0,l.useState)(!1),[b,v]=(0,l.useState)(!1),y=async()=>{if(u)try{let e=await (0,d.x7)((0,d.H9)(i.db,"users",u.uid));if(e.exists()){let t={id:e.id,...e.data()},r=(0,n.gZ)(t);r&&!t.trialExpired&&(await (0,d.mZ)((0,d.H9)(i.db,"users",u.uid),{trialExpired:!0,updatedAt:new Date}),t.trialExpired=!0),c(t),w("admin"===t.role),p((0,n.nE)(t)),v(r)}}catch(e){console.error("Error refreshing user data:",e)}};(0,l.useEffect)(()=>{let e=(0,s.hg)(i.j2,async e=>{x(e),e?await y():(c(null),w(!1),p(!1),v(!1)),m(!1)});return()=>e()},[]),(0,l.useEffect)(()=>{u&&y()},[u]);let k=async(e,t)=>{await (0,s.x9)(i.j2,e,t)},j=async(e,t)=>{let r=await (0,s.eJ)(i.j2,e,t),a=(0,n.ow)(r.user.email||e);await (0,d.BN)((0,d.H9)(i.db,"users",r.user.uid),a)},N=async()=>{await (0,s.CI)(i.j2)},E=async()=>{let e=new s.HF,t=await (0,s.df)(i.j2,e);if(!(await (0,d.x7)((0,d.H9)(i.db,"users",t.user.uid))).exists()){var r,a;let e=(0,n.ow)(t.user.email||"",{firstName:null==(r=t.user.displayName)?void 0:r.split(" ")[0],lastName:null==(a=t.user.displayName)?void 0:a.split(" ").slice(1).join(" ")});await (0,d.BN)((0,d.H9)(i.db,"users",t.user.uid),e)}},S=async()=>{let e=new s.sk,t=await (0,s.df)(i.j2,e);if(!(await (0,d.x7)((0,d.H9)(i.db,"users",t.user.uid))).exists()){var r,a;let e=(0,n.ow)(t.user.email||"",{firstName:null==(r=t.user.displayName)?void 0:r.split(" ")[0],lastName:null==(a=t.user.displayName)?void 0:a.split(" ").slice(1).join(" ")});await (0,d.BN)((0,d.H9)(i.db,"users",t.user.uid),e)}};return(0,a.jsx)(o.Provider,{value:{user:r,firebaseUser:u,loading:h,signIn:k,signUp:j,logOut:N,signInWithGoogle:E,signInWithFacebook:S,isAdmin:g,canAccessPremiumFeatures:f,isTrialExpired:b,refreshUserData:y},children:t})},u=()=>{let e=(0,l.useContext)(o);if(!e)throw Error("useAuth must be used within an AuthProvider");return e}},3391:(e,t,r)=>{r.d(t,{Q:()=>i,ThemeProvider:()=>d});var a=r(5155),l=r(2115);let s=(0,l.createContext)(null),i=()=>(0,l.useContext)(s),d=e=>{let{children:t}=e,[r,i]=(0,l.useState)("light"),[d,n]=(0,l.useState)(!1);(0,l.useEffect)(()=>{n(!0);let e=localStorage.getItem("theme");if(e)i(e),o(e);else{let e=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light";i(e),o(e)}},[]);let o=e=>{let t=document.documentElement;"dark"===e?t.classList.add("dark"):t.classList.remove("dark")};return d?(0,a.jsx)(s.Provider,{value:{theme:r,toggleTheme:()=>{let e="light"===r?"dark":"light";i(e),o(e),localStorage.setItem("theme",e)}},children:t}):(0,a.jsx)(a.Fragment,{children:t})}},8679:(e,t,r)=>{r.d(t,{A:()=>o,I:()=>c});var a=r(5155),l=r(2115),s=r(6874),i=r.n(s),d=r(3274),n=r(9886);function o(){let{user:e}=(0,d.A)(),[t,r]=(0,l.useState)(!1);if(!e||!e.isTrialUser||"admin"===e.role||t)return null;let{message:s,type:o,daysRemaining:c}=(0,n.Mo)(e);if(!s)return null;let u=()=>{switch(o){case"error":return"text-red-400 dark:text-red-300";case"warning":return"text-yellow-400 dark:text-yellow-300";default:return"text-blue-400 dark:text-blue-300"}};return(0,a.jsx)("div",{className:"border-l-4 p-4 ".concat((()=>{switch(o){case"error":return"bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800 text-red-800 dark:text-red-200";case"warning":return"bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800 text-yellow-800 dark:text-yellow-200";default:return"bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800 text-blue-800 dark:text-blue-200"}})()),children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:"error"===o?(0,a.jsx)("svg",{className:"h-5 w-5 ".concat(u()),viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})}):"warning"===o?(0,a.jsx)("svg",{className:"h-5 w-5 ".concat(u()),viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})}):(0,a.jsx)("svg",{className:"h-5 w-5 ".concat(u()),viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z",clipRule:"evenodd"})})}),(0,a.jsx)("div",{className:"ml-3",children:(0,a.jsx)("p",{className:"text-sm font-medium",children:s})})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(i(),{href:"/pricing",className:"px-4 py-2 rounded-md text-sm font-medium transition-colors ".concat((()=>{switch(o){case"error":return"bg-red-600 dark:bg-red-700 hover:bg-red-700 dark:hover:bg-red-800 text-white";case"warning":return"bg-yellow-600 dark:bg-yellow-700 hover:bg-yellow-700 dark:hover:bg-yellow-800 text-white";default:return"bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-800 text-white"}})()),children:"Upgrade Now"}),(0,a.jsx)("button",{onClick:()=>r(!0),className:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300","aria-label":"Dismiss banner",children:(0,a.jsx)("svg",{className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"})})})]})]})})}function c(){let{user:e}=(0,d.A)();if(!e||!e.isTrialUser||"admin"===e.role)return null;let{daysRemaining:t}=(0,n.Mo)(e);return t<=0?(0,a.jsx)(i(),{href:"/pricing",className:"px-3 py-1 bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 text-xs font-medium rounded-full hover:bg-red-200 dark:hover:bg-red-800 transition-colors",children:"Trial Expired"}):(0,a.jsxs)(i(),{href:"/pricing",className:"px-3 py-1 text-xs font-medium rounded-full transition-colors ".concat(t<=3?"bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 hover:bg-yellow-200 dark:hover:bg-yellow-800":"bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 hover:bg-blue-200 dark:hover:bg-blue-800"),children:[t," day",1===t?"":"s"," left"]})}},9886:(e,t,r)=>{function a(e){return!!e.isTrialUser&&!!e.trialEndDate&&new Date>new Date(e.trialEndDate)}function l(e){return!("admin"!==e.role&&(!e.subscription||"active"!==e.subscription.status)&&(!e.isTrialUser||a(e)))}function s(e){if(!e.isTrialUser)return{message:"",type:"info",daysRemaining:0};let t=function(e){if(!e.isTrialUser||!e.trialEndDate)return 0;let t=new Date;return Math.max(0,Math.ceil((new Date(e.trialEndDate).getTime()-t.getTime())/864e5))}(e);return t<=0?{message:"Your trial has expired. Upgrade to continue using premium features.",type:"error",daysRemaining:0}:t<=3?{message:"Your trial expires in ".concat(t," day").concat(1===t?"":"s",". Upgrade now to continue."),type:"warning",daysRemaining:t}:{message:"Your trial expires in ".concat(t," days on ").concat(e.trialEndDate?new Date(e.trialEndDate).toLocaleDateString("en-US",{weekday:"long",year:"numeric",month:"long",day:"numeric"}):"","."),type:"info",daysRemaining:t}}function i(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return{email:e,role:"user",isActive:!0,createdAt:new Date,...function(){let e=new Date,t=new Date(e);return t.setDate(e.getDate()+10),{isTrialUser:!0,trialStartDate:e,trialEndDate:t,trialExpired:!1}}(),...t}}r.d(t,{Mo:()=>s,gZ:()=>a,nE:()=>l,ow:()=>i})}}]);