(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{1138:(e,t,a)=>{"use strict";a.d(t,{IG:()=>o,db:()=>d,j2:()=>u});var r=a(3915),i=a(6203),s=a(5317),n=a(858);let l=(0,r.Dk)().length?(0,r.Sx)():(0,r.Wp)({apiKey:"AIzaSyDk_NWWIicxaEXYSWIoM-24-d9QrnrMhrg",authDomain:"ai-app-d5ee6.firebaseapp.com",projectId:"ai-app-d5ee6",storageBucket:"ai-app-d5ee6.firebasestorage.app",messagingSenderId:"81717928007",appId:"1:81717928007:web:c21e9ccb3abae564f29162"}),u=(0,i.xI)(l),d=(0,s.aU)(l),o=(0,n.c7)(l)},2175:(e,t,a)=>{Promise.resolve().then(a.bind(a,3274)),Promise.resolve().then(a.bind(a,3391)),Promise.resolve().then(a.t.bind(a,9840,23)),Promise.resolve().then(a.t.bind(a,9324,23))},3274:(e,t,a)=>{"use strict";a.d(t,{A:()=>c,AuthProvider:()=>o});var r=a(5155),i=a(2115),s=a(6203),n=a(1138),l=a(5317),u=a(9886);let d=(0,i.createContext)(null),o=e=>{let{children:t}=e,[a,o]=(0,i.useState)(null),[c,m]=(0,i.useState)(null),[p,w]=(0,i.useState)(!0),[g,h]=(0,i.useState)(!1),[f,y]=(0,i.useState)(!1),[x,v]=(0,i.useState)(!1),E=async()=>{if(c)try{let e=await (0,l.x7)((0,l.H9)(n.db,"users",c.uid));if(e.exists()){let t={id:e.id,...e.data()},a=(0,u.gZ)(t);a&&!t.trialExpired&&(await (0,l.mZ)((0,l.H9)(n.db,"users",c.uid),{trialExpired:!0,updatedAt:new Date}),t.trialExpired=!0),o(t),h("admin"===t.role),y((0,u.nE)(t)),v(a)}}catch(e){console.error("Error refreshing user data:",e)}};(0,i.useEffect)(()=>{let e=(0,s.hg)(n.j2,async e=>{m(e),e?await E():(o(null),h(!1),y(!1),v(!1)),w(!1)});return()=>e()},[]),(0,i.useEffect)(()=>{c&&E()},[c]);let b=async(e,t)=>{await (0,s.x9)(n.j2,e,t)},D=async(e,t)=>{let a=await (0,s.eJ)(n.j2,e,t),r=(0,u.ow)(a.user.email||e);await (0,l.BN)((0,l.H9)(n.db,"users",a.user.uid),r)},S=async()=>{await (0,s.CI)(n.j2)},N=async()=>{let e=new s.HF,t=await (0,s.df)(n.j2,e);if(!(await (0,l.x7)((0,l.H9)(n.db,"users",t.user.uid))).exists()){var a,r;let e=(0,u.ow)(t.user.email||"",{firstName:null==(a=t.user.displayName)?void 0:a.split(" ")[0],lastName:null==(r=t.user.displayName)?void 0:r.split(" ").slice(1).join(" ")});await (0,l.BN)((0,l.H9)(n.db,"users",t.user.uid),e)}},k=async()=>{let e=new s.sk,t=await (0,s.df)(n.j2,e);if(!(await (0,l.x7)((0,l.H9)(n.db,"users",t.user.uid))).exists()){var a,r;let e=(0,u.ow)(t.user.email||"",{firstName:null==(a=t.user.displayName)?void 0:a.split(" ")[0],lastName:null==(r=t.user.displayName)?void 0:r.split(" ").slice(1).join(" ")});await (0,l.BN)((0,l.H9)(n.db,"users",t.user.uid),e)}};return(0,r.jsx)(d.Provider,{value:{user:a,firebaseUser:c,loading:p,signIn:b,signUp:D,logOut:S,signInWithGoogle:N,signInWithFacebook:k,isAdmin:g,canAccessPremiumFeatures:f,isTrialExpired:x,refreshUserData:E},children:t})},c=()=>{let e=(0,i.useContext)(d);if(!e)throw Error("useAuth must be used within an AuthProvider");return e}},3391:(e,t,a)=>{"use strict";a.d(t,{Q:()=>n,ThemeProvider:()=>l});var r=a(5155),i=a(2115);let s=(0,i.createContext)(null),n=()=>(0,i.useContext)(s),l=e=>{let{children:t}=e,[a,n]=(0,i.useState)("light"),[l,u]=(0,i.useState)(!1);(0,i.useEffect)(()=>{u(!0);let e=localStorage.getItem("theme");if(e)n(e),d(e);else{let e=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light";n(e),d(e)}},[]);let d=e=>{let t=document.documentElement;"dark"===e?t.classList.add("dark"):t.classList.remove("dark")};return l?(0,r.jsx)(s.Provider,{value:{theme:a,toggleTheme:()=>{let e="light"===a?"dark":"light";n(e),d(e),localStorage.setItem("theme",e)}},children:t}):(0,r.jsx)(r.Fragment,{children:t})}},9324:()=>{},9840:e=>{e.exports={style:{fontFamily:"'Inter', 'Inter Fallback'",fontStyle:"normal"},className:"__className_e8ce0c"}},9886:(e,t,a)=>{"use strict";function r(e){return!!e.isTrialUser&&!!e.trialEndDate&&new Date>new Date(e.trialEndDate)}function i(e){return!("admin"!==e.role&&(!e.subscription||"active"!==e.subscription.status)&&(!e.isTrialUser||r(e)))}function s(e){if(!e.isTrialUser)return{message:"",type:"info",daysRemaining:0};let t=function(e){if(!e.isTrialUser||!e.trialEndDate)return 0;let t=new Date;return Math.max(0,Math.ceil((new Date(e.trialEndDate).getTime()-t.getTime())/864e5))}(e);return t<=0?{message:"Your trial has expired. Upgrade to continue using premium features.",type:"error",daysRemaining:0}:t<=3?{message:"Your trial expires in ".concat(t," day").concat(1===t?"":"s",". Upgrade now to continue."),type:"warning",daysRemaining:t}:{message:"Your trial expires in ".concat(t," days on ").concat(e.trialEndDate?new Date(e.trialEndDate).toLocaleDateString("en-US",{weekday:"long",year:"numeric",month:"long",day:"numeric"}):"","."),type:"info",daysRemaining:t}}function n(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return{email:e,role:"user",isActive:!0,createdAt:new Date,...function(){let e=new Date,t=new Date(e);return t.setDate(e.getDate()+10),{isTrialUser:!0,trialStartDate:e,trialEndDate:t,trialExpired:!1}}(),...t}}a.d(t,{Mo:()=>s,gZ:()=>r,nE:()=>i,ow:()=>n})}},e=>{var t=t=>e(e.s=t);e.O(0,[385,992,965,288,441,684,358],()=>t(2175)),_N_E=e.O()}]);