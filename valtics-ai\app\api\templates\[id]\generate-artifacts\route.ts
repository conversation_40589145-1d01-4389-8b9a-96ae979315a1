import { NextRequest, NextResponse } from 'next/server';
import { doc, updateDoc, getDoc, Timestamp } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const templateId = params.id;
    const { selectedLLM, companyName } = await request.json();

    // Validate input
    if (!selectedLLM || !['claude', 'openai'].includes(selectedLLM)) {
      return NextResponse.json(
        { error: 'Invalid LLM selection' },
        { status: 400 }
      );
    }

    // Get template data
    const templateDoc = await getDoc(doc(db, 'templates', templateId));
    if (!templateDoc.exists()) {
      return NextResponse.json(
        { error: 'Template not found' },
        { status: 404 }
      );
    }

    const templateData = templateDoc.data();

    // Check if required files are uploaded
    if (!templateData.enterpriseNeedFileUrl || 
        !templateData.solutionDescriptionFileUrl || 
        !templateData.riskOfNoInvestmentFileUrl) {
      return NextResponse.json(
        { error: 'All three document files must be uploaded before generating artifacts' },
        { status: 400 }
      );
    }

    // Update status to processing
    await updateDoc(doc(db, 'templates', templateId), {
      status: 'artifacts_generating',
      selectedLLM,
      updatedAt: Timestamp.now()
    });

    // Generate the prompt
    const prompt = `Generate three visual artifacts presented as they appear in the three attached files. The content should be in an executive-level tone, a descriptive format, with no bullet points. Use complete sentences that are professionally structured. Use a wide format, like a PowerPoint-ready format. Each of the three artifacts begins with a header containing the [${templateData.solutionName}], followed by [Business Value Assessment]. In each artifact, also include a subtitle labeled [${companyName || 'Company Name'}]. In the first artifact, include a section subtitle labeled 'Enterprise Need' with the corresponding need description. In the second artifact, include a section subtitle labeled 'Proposed Solution' with the corresponding solution description. In the third artifact, include a section subtitle labeled 'Risk of No Investment' with the corresponding solution description. Use only the content from these three attached files and do not add any other information other than the items requested in this prompt.`;

    // Store the prompt in the template
    await updateDoc(doc(db, 'templates', templateId), {
      aiPrompt: prompt,
      updatedAt: Timestamp.now()
    });

    // Start asynchronous processing
    processAIGeneration(templateId, selectedLLM, prompt, templateData);

    return NextResponse.json({
      success: true,
      message: 'AI artifact generation started',
      prompt
    });

  } catch (error) {
    console.error('Error starting AI generation:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

async function processAIGeneration(
  templateId: string,
  selectedLLM: string,
  prompt: string,
  templateData: any
) {
  try {
    // Simulate AI processing (replace with actual API calls)
    await new Promise(resolve => setTimeout(resolve, 5000));

    // Mock generated artifacts (replace with actual AI API responses)
    const generatedArtifacts = {
      enterpriseNeedArtifact: `# ${templateData.solutionName}\n## Business Value Assessment\n### ${templateData.companyName || 'Company Name'}\n\n#### Enterprise Need\n\nThis is a mock generated artifact for the enterprise need section. In a real implementation, this would be generated by ${selectedLLM} API based on the uploaded document content.`,
      solutionArtifact: `# ${templateData.solutionName}\n## Business Value Assessment\n### ${templateData.companyName || 'Company Name'}\n\n#### Proposed Solution\n\nThis is a mock generated artifact for the proposed solution section. In a real implementation, this would be generated by ${selectedLLM} API based on the uploaded document content.`,
      riskArtifact: `# ${templateData.solutionName}\n## Business Value Assessment\n### ${templateData.companyName || 'Company Name'}\n\n#### Risk of No Investment\n\nThis is a mock generated artifact for the risk of no investment section. In a real implementation, this would be generated by ${selectedLLM} API based on the uploaded document content.`,
      generatedAt: new Date()
    };

    // Update template with generated artifacts
    await updateDoc(doc(db, 'templates', templateId), {
      status: 'draft',
      generatedArtifacts,
      step3Completed: true,
      updatedAt: Timestamp.now()
    });

  } catch (error) {
    console.error('Error processing AI generation:', error);
    
    // Update status to indicate error
    await updateDoc(doc(db, 'templates', templateId), {
      status: 'draft',
      updatedAt: Timestamp.now()
    });
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const templateId = params.id;
    
    // Get current template status
    const templateDoc = await getDoc(doc(db, 'templates', templateId));
    if (!templateDoc.exists()) {
      return NextResponse.json(
        { error: 'Template not found' },
        { status: 404 }
      );
    }

    const templateData = templateDoc.data();
    
    return NextResponse.json({
      status: templateData.status,
      generatedArtifacts: templateData.generatedArtifacts,
      step3Completed: templateData.step3Completed || false
    });

  } catch (error) {
    console.error('Error getting template status:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
